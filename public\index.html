<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Before Six</title>
    <!-- Load TailwindCSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Load React and ReactDOM from a CDN -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <!-- Load Babel Standalone from a CDN -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <!-- The App.js file will be loaded and transpiled by Babel -->
  </head>
  <body>
    <div id="root"></div>
    <script type="text/babel" data-presets="react,typescript" src="App.js"></script>
  </body>
</html>
