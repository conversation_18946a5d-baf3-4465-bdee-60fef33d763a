// BEFORE SIX — Enhanced single-file app with Supabase auth, restaurant dashboard, and complete booking flow
// • Supabase authentication for customers and restaurants
// • Enhanced restaurant discovery with detailed filtering
// • Complete booking flow with payment processing
// • Restaurant partner dashboard with settings
// • Customer account management
// • Multilingual support (EN/繁中)
// • Mobile-optimized UI
// • Comprehensive legal pages

// ---------------- i18n ----------------
const i18n = {
  en: {
    brand: "Before Six",
    subtitle: "Early seats, better nights — 30–50% off food",
    explore: "Explore",
    myBookings: "My Bookings",
    dashboard: "Restaurant Dashboard",
    account: "My Account",
    login: "Sign In",
    logout: "Sign Out",
    register: "Sign Up",
    tonight: "Tonight's Early Seats",
    filters: "Filters",
    area: "Area",
    cuisine: "Cuisine",
    discount: "Discount",
    time: "Time",
    party: "Party size",
    drinkMinimum: "Drink minimum",
    sortBy: "Sort",
    sort_relevance: "Relevance",
    sort_soonest: "Soonest",
    sort_discount: "Highest discount",
    clearAll: "Clear all",
    results: (n)=>`${n} results`,
    bookingFee: "Booking fee",
    feeNote: "(charged by us; restaurant settles your bill)",
    holdPolicy: "Your table is held for 10 minutes after your window begins.",
    included: "What's included",
    excluded: "What's not included",
    rules: "House rules",
    viewDetails: "Open ticket",
    reserve: "Reserve seat",
    selectWindow: "Choose an arrival window",
    selectPax: "Party size",
    name: "Name",
    email: "Email",
    phone: "Phone",
    password: "Password",
    confirmPassword: "Confirm password",
    agree: "I agree to the venue's T&Cs, including the 10-minute hold.",
    confirm: (fee) => `Confirm (HK$${fee})`,
    successTitle: "You're in!",
    successBody: "We've emailed your ticket. Add it to your calendar.",
    langToggle: "繁",
    noResults: "No results — try adjusting filters.",
    manage: "Manage",
    cancel: "Cancel booking",
    cancelled: "Cancelled",
    active: "Active",
    addCalendar: "Add to calendar (.ics)",
    copyMap: "Open map",
    globalPolicy: "Global policy: 10-minute hold on all bookings.",
    passwordsDontMatch: "Passwords do not match",
    // Authentication
    auth_welcome: "Welcome to Before Six",
    auth_login: "Sign In",
    auth_register: "Sign Up",
    auth_already: "Already have an account?",
    auth_noAccount: "Don't have an account?",
    auth_signInAs: "Sign in as",
    auth_signUpAs: "Sign up as",
    auth_customer: "Customer",
    auth_restaurant: "Restaurant",
    // Dashboard
    dash_login: "Sign in as restaurant",
    yourEmail: "Your work email",
    sendMagic: "Send magic link (demo)",
    signedInAs: (n)=>`Signed in as ${n}`,
    venueSelect: "Select venue",
    newWindow: "Publish window",
    start: "Start",
    end: "End",
    discountPct: "Discount %",
    minPax: "Min pax",
    maxPax: "Max pax",
    cap: "Cap",
    pattern: "Pattern",
    once: "One-off (date)",
    weekly: "Weekly (weekday)",
    date: "Date",
    weekday: "Weekday",
    publish: "Publish",
    ruleTemplates: "Quick rules",
    rule_bev: "Each diner must order a beverage",
    rule_time: "Time limit 90 minutes",
    rule_foodOnly: "Discount applies to food only",
    rulesSaved: "Rules saved",
    todaysBookings: "Today's bookings",
    status: "Status",
    none: "None",
    save: "Save",
    delete: "Delete",
    // Restaurant Settings
    settings: "Restaurant Settings",
    maxPartySize: "Maximum party size",
    beverageMin: "One beverage minimum per guest",
    menuExclusions: "Menu item exclusions",
    timeSlots: "Time slots",
    availability: "Availability",
    profile: "Profile",
    photos: "Photos",
    description: "Description",
    cuisineTags: "Cuisine tags",
    // Enhanced Booking
    bookingSummary: "Booking Summary",
    total: "Total",
    payNow: "Pay Now",
    paymentSuccess: "Payment Successful",
    paymentFailed: "Payment Failed",
    // Customer Account
    profile: "Profile",
    favorites: "Favorites",
    notifications: "Notifications",
    bookingHistory: "Booking History",
    upcoming: "Upcoming",
    past: "Past",
    completed: "Completed",
    // Legal
    about: "About Us",
    faq: "FAQ",
    contact: "Contact",
    terms: "Terms & Conditions",
    privacy: "Privacy Policy",
    support: "Customer Support",
    // Footer
    copyright: "© 2025 Before Six. All rights reserved.",
    companyReg: "Company Registration: ********",
    supportHours: "Mon-Fri: 9am-6pm, Sat-Sun: 10am-4pm",
    supportPhone: "+852 1234 5678",
    supportEmail: "<EMAIL>",
  },
  zh: {
    brand: "Before Six",
    subtitle: "早到好味道 — 食物 30–50% 折扣",
    explore: "探索",
    myBookings: "我的訂座",
    dashboard: "餐廳後台",
    account: "我的帳戶",
    login: "登入",
    logout: "登出",
    register: "註冊",
    tonight: "今晚早到座位",
    filters: "篩選",
    area: "區域",
    cuisine: "菜系",
    discount: "折扣",
    time: "時段",
    party: "人數",
    drinkMinimum: "飲品最低消費",
    sortBy: "排序",
    sort_relevance: "相關度",
    sort_soonest: "最快時間",
    sort_discount: "最高折扣",
    clearAll: "清除全部",
    results: (n)=>`${n} 個結果`,
    bookingFee: "訂座手續費",
    feeNote: "（由平台收取；餐廳現場結帳）",
    holdPolicy: "到席時段開始後保留 10 分鐘。",
    included: "優惠包括",
    excluded: "不包括",
    rules: "用餐條款",
    viewDetails: "開啟票券",
    reserve: "預留座位",
    selectWindow: "選擇到席時段",
    selectPax: "選擇人數",
    name: "姓名",
    email: "電郵",
    phone: "電話",
    password: "密碼",
    confirmPassword: "確認密碼",
    agree: "本人同意餐廳條款（包括 10 分鐘保留期）。",
    confirm: (fee) => `確認（HK$${fee}）`,
    successTitle: "已預留！",
    successBody: "確認信已發送。加入行事曆以免錯過。",
    langToggle: "EN",
    noResults: "未有結果，試試調整篩選。",
    manage: "管理",
    cancel: "取消訂座",
    cancelled: "已取消",
    active: "生效中",
    addCalendar: "加入行事曆 (.ics)",
    copyMap: "開啟地圖",
    globalPolicy: "平台統一政策：所有訂座均設 10 分鐘保留期。",
    passwordsDontMatch: "密碼不匹配",
    // Authentication
    auth_welcome: "歡迎使用 Before Six",
    auth_login: "登入",
    auth_register: "註冊",
    auth_already: "已有帳戶？",
    auth_noAccount: "沒有帳戶？",
    auth_signInAs: "以身份登入",
    auth_signUpAs: "以身份註冊",
    auth_customer: "顧客",
    auth_restaurant: "餐廳",
    // Dashboard
    dash_login: "以餐廳身份登入",
    yourEmail: "工作電郵",
    sendMagic: "發送登入連結（示範）",
    signedInAs: (n)=>`登入為 ${n}`,
    venueSelect: "選擇餐廳",
    newWindow: "發佈時段",
    start: "開始",
    end: "結束",
    discountPct: "折扣 %",
    minPax: "最少人數",
    maxPax: "最多人數",
    cap: "名額",
    pattern: "模式",
    once: "單次（日期）",
    weekly: "每週（星期）",
    date: "日期",
    weekday: "星期",
    publish: "發佈",
    ruleTemplates: "快捷條款",
    rule_bev: "每位需點一款飲品",
    rule_time: "用餐時間 90 分鐘",
    rule_foodOnly: "折扣只適用於食物",
    rulesSaved: "已儲存條款",
    todaysBookings: "今日訂座",
    status: "狀態",
    none: "沒有",
    save: "儲存",
    delete: "刪除",
    // Restaurant Settings
    settings: "餐廳設定",
    maxPartySize: "最多人數",
    beverageMin: "每位客人需點一款飲品",
    menuExclusions: "菜單項目排除",
    timeSlots: "時段",
    availability: "可預訂時間",
    profile: "資料",
    photos: "照片",
    description: "描述",
    cuisineTags: "菜系標籤",
    // Enhanced Booking
    bookingSummary: "訂座摘要",
    total: "總計",
    payNow: "立即付款",
    paymentSuccess: "付款成功",
    paymentFailed: "付款失敗",
    // Customer Account
    profile: "個人資料",
    favorites: "最愛",
    notifications: "通知設定",
    bookingHistory: "訂座歷史",
    upcoming: "即將到來",
    past: "過往",
    completed: "已完成",
    // Legal
    about: "關於我們",
    faq: "常見問題",
    contact: "聯絡我們",
    terms: "條款與細則",
    privacy: "私隱政策",
    support: "客戶支援",
    // Footer
    copyright: "© 2025 Before Six. 保留所有權利。",
    companyReg: "公司註冊：********",
    supportHours: "週一至五：上午9時-下午6時，週末：上午10時-下午4時",
    supportPhone: "+852 1234 5678",
    supportEmail: "<EMAIL>",
  }
}

// ---------------- Constants ----------------
/** User roles */
const USER_ROLES = {
  CUSTOMER: 'customer',
  RESTAURANT: 'restaurant'
};

/** Window pattern */
const PATTERN_TYPES = {
  ONCE: 'once',
  WEEKLY: 'weekly'
};

/** Booking status */
const BOOKING_STATUS = {
  ACTIVE: 'active',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed'
};

// ---------------- Types ----------------
/** User type */
const User = {
  id: '',
  email: '',
  role: '',
  name: '',
  phone: '',
  avatar: '',
  preferences: {
    language: 'en',
    notifications: true
  }
};

/** Restaurant type */
const Restaurant = {
  id: '',
  name: '',
  area: '',
  address: '',
  lat: 0,
  lng: 0,
  cuisine: '',
  priceBand: '',
  blurb: {en: '', zh: ''},
  photos: [],
  rules: [],
  settings: {
    maxPartySize: 4,
    hasBeverageMinimum: false,
    excludedItems: [],
    timeSlots: []
  }
};

/** Time slot type */
const TimeSlot = {
  id: '',
  restaurantId: '',
  pattern: '',
  date: '',
  dow: 0,
  start: '',
  end: '',
  discount: 0,
  minPax: 0,
  maxPax: 0,
  cap: 0,
  capRemaining: 0,
  isActive: false
};

/** Booking type */
const Booking = {
  id: '',
  windowId: '',
  restaurantId: '',
  userId: '',
  name: '',
  email: '',
  phone: '',
  pax: 0,
  lang: 'en',
  status: '',
  code: '',
  paymentStatus: 'pending',
  paymentId: '',
  createdAt: '',
  updatedAt: ''
};

// ---------------- Utilities ----------------
const uid = () => Math.random().toString(36).slice(2);
const todayISO = () => new Date().toISOString().slice(0,10);
function dowOf(dateISO){ return new Date(dateISO+"T00:00:00").getDay(); }
function toICS(booking, rest, slot){
  // simplistic ICS generator
  const startDate = new Date(`${slot.date ?? todayISO()}T${slot.start}:00`);
  const endDate = new Date(`${slot.date ?? todayISO()}T${slot.end}:00`);
  const dt = (d)=>d.toISOString().replace(/[-:]/g,"").split(".")[0]+"Z";
  const body = [
    "BEGIN:VCALENDAR","VERSION:2.0","PRODID://Before Six//Booking//EN","BEGIN:VEVENT",
    `UID:${booking.id}@beforesix.hk`,
    `DTSTAMP:${dt(new Date())}`,
    `DTSTART:${dt(startDate)}`,
    `DTEND:${dt(endDate)}`,
    `SUMMARY:Before Six — ${rest.name}`,
    `DESCRIPTION:${rest.address} — Hold 10 mins after window start`,
    "END:VEVENT","END:VCALENDAR"
  ].join("\r\n");
  return new Blob([body], {type:"text/calendar"});
}

function getBookingFee(pax) {
  switch (pax) {
    case 2:
      return 50;
    case 3:
      return 70;
    case 4:
      return 80;
    case 5:
      return 100;
    case 6:
      return 120;
    default:
      return 0;
  }
}

// ---------------- Supabase Configuration ----------------
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = 'your-anon-key';
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// ---------------- Demo seed data ----------------
const SEED_RESTAURANTS = [
  { 
    id:"r1", 
    name:"Kin & Coal", 
    area:"Central", 
    address:"18 Staunton St, Central", 
    cuisine:"Modern European", 
    blurb:{en:"Seasonal grill & small plates in SoHo.", zh:"主打時令火烤與精緻小食，位於 SoHo。"}, 
    photos:["https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1200&auto=format&fit=crop"], 
    rules:["Each diner must order one beverage","Discount applies to food only"],
    settings: {
      maxPartySize: 4,
      hasBeverageMinimum: true,
      excludedItems: ["Premium steaks", "Wine pairing"],
      timeSlots: []
    }
  },
  { 
    id:"r2", 
    name:"Umi Izakaya", 
    area:"Tsim Sha Tsui", 
    address:"Harbour City, TST", 
    cuisine:"Japanese", 
    blurb:{en:"Charcoal seafood izakaya.", zh:"炭火海鮮居酒屋。"}, 
    photos:["https://images.unsplash.com/photo-1559339351-3794a0af83d1?q=80&w=1200&auto=format&fit=crop"], 
    rules:["Each diner must order one beverage","Selected items excluded","Discount applies to food only"],
    settings: {
      maxPartySize: 6,
      hasBeverageMinimum: true,
      excludedItems: ["Omakase", "Premium sashimi"],
      timeSlots: []
    }
  },
  { 
    id:"r3", 
    name:"Pasta Via", 
    area:"Wan Chai", 
    address:"Jaffe Rd, Wan Chai", 
    cuisine:"Italian", 
    blurb:{en:"Fresh pasta, neighborhood vibe.", zh:"手造意粉，街坊小店氛圍。"}, 
    photos:["https://images.unsplash.com/photo-1517959105821-eaf2591984f5?q=80&w=1200&auto=format&fit=crop"], 
    rules:["Discount applies to food only"],
    settings: {
      maxPartySize: 4,
      hasBeverageMinimum: false,
      excludedItems: [],
      timeSlots: []
    }
  },
];

const SEED_WINDOWS = [
  { id:"w1", restaurantId:"r1", pattern:PATTERN_TYPES.ONCE, date: todayISO(), start:"17:00", end:"18:00", discount:40, minPax:2, maxPax:4, cap:4, capRemaining:4, isActive:true },
  { id:"w2", restaurantId:"r1", pattern:PATTERN_TYPES.ONCE, date: todayISO(), start:"21:30", end:"22:30", discount:40, minPax:2, maxPax:4, cap:2, capRemaining:2, isActive:true },
  { id:"w3", restaurantId:"r2", pattern:PATTERN_TYPES.ONCE, date: todayISO(), start:"17:30", end:"18:30", discount:50, minPax:2, maxPax:6, cap:6, capRemaining:6, isActive:true },
  { id:"w4", restaurantId:"r3", pattern:PATTERN_TYPES.ONCE, date: todayISO(), start:"18:00", end:"18:45", discount:30, minPax:2, maxPax:4, cap:3, capRemaining:3, isActive:true },
];

// ---------------- Storage (localStorage) ----------------
function usePersistentState(key, initial){
  const [state, setState] = React.useState(()=>{
    const raw = localStorage.getItem(key);
    return raw? JSON.parse(raw): initial;
  });
  React.useEffect(()=>{ 
    if (state !== null && state !== undefined) {
      localStorage.setItem(key, JSON.stringify(state)); 
    }
  },[key,state]);
  return [state, setState];
}

// ---------------- Authentication ----------------
async function signUp(email, password, role, name = '', phone = '') {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        role,
        name,
        phone
      }
    }
  });
  return { data, error };
}

async function signIn(email, password) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  return { data, error };
}

async function signOut() {
  const { error } = await supabase.auth.signOut();
  return { error };
}

function useAuth() {
  const [user, setUser] = React.useState(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user || null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user || null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  return { user, loading };
}

// ---------------- UI helpers ----------------
function Header({lang, setLang, view, setView, user, onLogout, onLogin, onRegister}){
  const t = i18n[lang];
  return (
    <header className="sticky top-0 z-40 border-b bg-white/80 backdrop-blur">
      <div className="mx-auto flex max-w-6xl items-center justify-between gap-4 px-4 py-3">
        <div className="flex items-baseline gap-3">
          <div className="font-serif text-2xl font-bold tracking-tight">{t.brand}</div>
          <div className="text-xs text-amber-800/80">HK</div>
        </div>
        
        <nav className="flex items-center gap-4 text-sm">
          {user ? (
            <>
              <button className={`rounded-xl px-3 py-1 ${view==='explore'? 'bg-amber-900 text-white':'border'}`} onClick={()=>setView('explore')} aria-current={view==='explore'}>{t.explore}</button>
              <button className={`rounded-xl px-3 py-1 ${view==='bookings'? 'bg-amber-900 text-white':'border'}`} onClick={()=>setView('bookings')}>{t.myBookings}</button>
              {user.role === USER_ROLES.RESTAURANT && (
                <button className={`rounded-xl px-3 py-1 ${view==='dashboard'? 'bg-amber-900 text-white':'border'}`} onClick={()=>setView('dashboard')}>{t.dashboard}</button>
              )}
              <button className={`rounded-xl px-3 py-1 ${view==='account'? 'bg-amber-900 text-white':'border'}`} onClick={()=>setView('account')}>{t.account}</button>
              <button onClick={onLogout} className="rounded-xl border px-3 py-1">{t.logout}</button>
            </>
          ) : (
            <>
              <button onClick={onLogin} className="rounded-xl border px-3 py-1">{t.login}</button>
              <button onClick={onRegister} className="rounded-xl border px-3 py-1">{t.register}</button>
            </>
          )}
          <button onClick={()=>setLang(lang==='en'?'zh':'en')} className="rounded-xl border px-3 py-1" aria-label="Toggle language">{i18n[lang].langToggle}</button>
        </nav>
      </div>
    </header>
  );
}

function Hero({lang}){
  const t = i18n[lang];
  return (
    <section className="relative mx-auto max-w-6xl overflow-hidden rounded-3xl border bg-gradient-to-br from-amber-50 via-white to-amber-100 p-6 md:p-10">
      <div className="pointer-events-none absolute -left-10 -top-10 h-56 w-56 rounded-full bg-amber-200/40 blur-3xl" />
      <div className="pointer-events-none absolute -bottom-12 -right-8 h-72 w-72 rounded-full bg-yellow-200/30 blur-3xl" />
      <div className="relative flex flex-col gap-3">
        <div className="text-xs uppercase tracking-wider text-amber-700/70">Hong Kong • Dining</div>
        <h1 className="font-serif text-3xl font-bold tracking-tight md:text-5xl">{t.brand}</h1>
        <p className="max-w-xl text-sm text-gray-700 md:text-base">{t.subtitle}</p>
        <div className="text-xs text-amber-800/80">{t.holdPolicy}</div>
      </div>
    </section>
  );
}

// ---------------- Authentication Modals ----------------
function useFocusTrap(open){
  const ref = React.useRef(null);
  React.useEffect(()=>{
    if(!open) return; 
    const el = ref.current; 
    if(!el) return;
    const prev = document.activeElement;
    const focusable = el.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    focusable[0]?.focus();
    function handler(e){ 
      if(e.key==='Tab'){ 
        const f = Array.from(focusable); 
        const i = f.indexOf(document.activeElement); 
        if(e.shiftKey){ 
          if(i<=0){ 
            e.preventDefault(); 
            f[f.length-1]?.focus(); 
          } 
        } else { 
          if(i===f.length-1){ 
            e.preventDefault(); 
            f[0]?.focus(); 
          } 
        } 
      } 
    }
    el.addEventListener('keydown', handler);
    return ()=>{ 
      el.removeEventListener('keydown', handler); 
      prev?.focus(); 
    };
  },[open]);
  return ref;
}

function AuthModal({open, onClose, mode, onModeChange, onAuthComplete}){
  const ref = useFocusTrap(open);
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [confirmPassword, setConfirmPassword] = React.useState('');
  const [name, setName] = React.useState('');
  const [phone, setPhone] = React.useState('');
  const [role, setRole] = React.useState(USER_ROLES.CUSTOMER);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState('');
  
  const t = i18n[window.currentLang || 'en'];
  
  const isSignUp = mode === 'signup';
  
  async function handleSubmit(e) {
    e.preventDefault();
    setError('');
    setLoading(true);
    
    try {
      if (isSignUp) {
        if (password !== confirmPassword) {
          setError(t.passwordsDontMatch);
          return;
        }
        
        const { data, error } = await signUp(email, password, role, name, phone);
        if (error) throw error;
        
        if (data.user) {
          onAuthComplete();
        }
      } else {
        const { data, error } = await signIn(email, password);
        if (error) throw error;
        
        if (data.user) {
          onAuthComplete();
        }
      }
    } catch (err) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  }
  
  if (!open) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center bg-black/40 p-4 md:items-center" role="dialog" aria-modal ref={ref}>
      <div className="max-h-[90vh] w-full max-w-md overflow-auto rounded-3xl bg-white p-6">
        <div className="flex items-center justify-between gap-4 mb-6">
          <h3 className="text-xl font-semibold">{isSignUp ? t.auth_register : t.auth_login}</h3>
          <button className="rounded-2xl border px-3 py-1" onClick={onClose}>Close</button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex gap-2 mb-4">
            <button 
              type="button"
              className={`flex-1 rounded-xl py-2 ${role === USER_ROLES.CUSTOMER ? 'bg-amber-900 text-white' : 'border'}`}
              onClick={() => setRole(USER_ROLES.CUSTOMER)}
            >
              {t.auth_customer}
            </button>
            <button 
              type="button"
              className={`flex-1 rounded-xl py-2 ${role === USER_ROLES.RESTAURANT ? 'bg-amber-900 text-white' : 'border'}`}
              onClick={() => setRole(USER_ROLES.RESTAURANT)}
            >
              {t.auth_restaurant}
            </button>
          </div>
          
          {isSignUp && (
            <>
              <div>
                <label className="block text-sm mb-1">{t.name}</label>
                <input 
                  type="text" 
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full rounded-2xl border px-3 py-2"
                  required={isSignUp}
                />
              </div>
              
              <div>
                <label className="block text-sm mb-1">{t.phone}</label>
                <input 
                  type="tel" 
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="w-full rounded-2xl border px-3 py-2"
                  placeholder="+852"
                />
              </div>
            </>
          )}
          
          <div>
            <label className="block text-sm mb-1">{t.email}</label>
            <input 
              type="email" 
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full rounded-2xl border px-3 py-2"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm mb-1">{t.password}</label>
            <input 
              type="password" 
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full rounded-2xl border px-3 py-2"
              required
            />
          </div>
          
          {isSignUp && (
            <div>
              <label className="block text-sm mb-1">{t.confirmPassword}</label>
              <input 
                type="password" 
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full rounded-2xl border px-3 py-2"
                required
              />
            </div>
          )}
          
          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}
          
          <button 
            type="submit" 
            disabled={loading}
            className="w-full rounded-2xl bg-amber-900 px-4 py-2 text-white disabled:opacity-50"
          >
            {loading ? 'Loading...' : (isSignUp ? t.auth_register : t.auth_login)}
          </button>
          
          <div className="text-center text-sm">
            {isSignUp ? (
              <>
                {t.auth_already}{' '}
                <button type="button" onClick={() => onModeChange('signin')} className="text-amber-900 underline">
                  {t.auth_login}
                </button>
              </>
            ) : (
              <>
                {t.auth_noAccount}{' '}
                <button type="button" onClick={() => onModeChange('signup')} className="text-amber-900 underline">
                  {t.auth_register}
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

// -------- Explore (filters + list) --------
function Explore({lang, restaurants, windows, onOpen, user}){
  const t = i18n[lang];
  const [filters, setFilters] = usePersistentState("filters", {area:"", cuisine:"", discount:0, party:2, drinkMinimum: null, sort:"relevance"});

  const list = React.useMemo(()=>{
    const today = todayISO();
    const active = windows.filter(w=> w.isActive && (w.date? w.date>=today : true) && w.capRemaining>0);
    const rows = active.map(w=>({w, r: restaurants.find(r=>r.id===w.restaurantId)})).filter(x=>!!x.r);
    let filtered = rows.filter(({r,w})=>{
      const okArea = !filters.area || r.area.toLowerCase().includes(filters.area.toLowerCase());
      const okCui = !filters.cuisine || r.cuisine.toLowerCase().includes(filters.cuisine.toLowerCase());
      const okDis = !filters.discount || w.discount>=filters.discount;
      const okPax = filters.party<=w.maxPax;
      const okDrink = filters.drinkMinimum === null || 
                     (filters.drinkMinimum === true && r.settings.hasBeverageMinimum) ||
                     (filters.drinkMinimum === false && !r.settings.hasBeverageMinimum);
      return okArea && okCui && okDis && okPax && okDrink;
    });
    if(filters.sort==='soonest') filtered.sort((a,b)=> (a.w.start).localeCompare(b.w.start));
    if(filters.sort==='discount') filtered.sort((a,b)=> b.w.discount - a.w.discount);
    return filtered;
  },[restaurants, windows, filters]);

  return (
    <section className="mx-auto max-w-6xl px-4 py-6">
      <h2 className="mb-3 text-lg font-semibold">{t.tonight}</h2>
      <div className="mb-3 grid grid-cols-2 gap-3 md:grid-cols-9">
        <input className="rounded-2xl border px-3 py-2" placeholder={t.area} value={filters.area} onChange={e=>setFilters({...filters, area:e.target.value})} />
        <input className="rounded-2xl border px-3 py-2" placeholder={t.cuisine} value={filters.cuisine} onChange={e=>setFilters({...filters, cuisine:e.target.value})} />
        <select className="rounded-2xl border px-3 py-2" value={filters.discount} onChange={e=>setFilters({...filters, discount:Number(e.target.value)})}>
          <option value={0}>{t.discount}</option>
          <option value={30}>≥ 30%</option>
          <option value={40}>≥ 40%</option>
          <option value={50}>≥ 50%</option>
        </select>
        <select className="rounded-2xl border px-3 py-2" value={filters.party} onChange={e=>setFilters({...filters, party:Number(e.target.value)})}>
          {[2,3,4,5,6].map(v=> <option key={v} value={v}>{v}</option>)}
        </select>
        <select className="rounded-2xl border px-3 py-2" value={filters.drinkMinimum ?? ''} onChange={e=>setFilters({...filters, drinkMinimum: e.target.value === '' ? null : e.target.value === 'true'})}>
          <option value="">{t.drinkMinimum}</option>
          <option value="true">Yes</option>
          <option value="false">No</option>
        </select>
        <select className="rounded-2xl border px-3 py-2" value={filters.sort} onChange={e=>setFilters({...filters, sort:e.target.value})}>
          <option value="relevance">{t.sort_relevance}</option>
          <option value="soonest">{t.sort_soonest}</option>
          <option value="discount">{t.sort_discount}</option>
        </select>
        <button className="rounded-2xl border px-3 py-2" onClick={()=>setFilters({area:"", cuisine:"", discount:0, party:2, drinkMinimum: null, sort:"relevance"})}>{t.clearAll}</button>
        <div className="self-center text-sm text-gray-600">{t.results(list.length)}</div>
      </div>

      {list.length===0? (
        <div className="rounded-3xl border p-8 text-center text-sm text-gray-600">{t.noResults}</div>
      ): (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {list.map(({r,w})=> (
            <EnhancedCard key={w.id} r={r} w={w} lang={lang} onOpen={()=>onOpen(r)} user={user} />
          ))}
        </div>
      )}

      <footer className="mt-8 text-center text-xs text-gray-600">{t.bookingFee} · {t.holdPolicy}</footer>
    </section>
  );
}

function EnhancedCard({r,w,lang,onOpen,user}){
  const t = i18n[lang];
  const [isFavorite, setIsFavorite] = React.useState(false);
  
  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // In a real app, this would update the user's favorites in the database
  };
  
  return (
    <div className="overflow-hidden rounded-3xl bg-white shadow-[0_8px_24px_rgba(0,0,0,0.06)]">
      <div className="relative">
        <img src={r.photos[0]} alt={r.name} className="h-44 w-full object-cover"/>
        {user && (
          <button 
            onClick={toggleFavorite}
            className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 backdrop-blur flex items-center justify-center"
            aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
          >
            {isFavorite ? '❤️' : '🤍'}
          </button>
        )}
      </div>
      <div className="space-y-3 p-4">
        <div className="flex items-start justify-between gap-3">
          <div>
            <h3 className="text-lg font-semibold">{r.name}</h3>
            <div className="text-sm text-gray-600">{r.area} • {r.cuisine}</div>
            <p className="mt-1 text-sm text-gray-700">{r.blurb[lang]}</p>
          </div>
          <div className="shrink-0 rounded-2xl border px-3 py-1 text-xs">{w.discount}% off</div>
        </div>
        
        <div className="flex items-center gap-2 text-xs">
          <span className="grid h-5 w-5 place-items-center rounded-full border">🕑</span>
          <span>{w.start}–{w.end}</span>
          <span className="ml-auto text-gray-500">{w.minPax}–{w.maxPax} pax</span>
        </div>
        
        <div className="flex flex-wrap gap-2 text-xs">
          {r.settings.maxPartySize && (
            <span className="rounded-full bg-amber-100 px-2 py-1 text-amber-800">
              Max {r.settings.maxPartySize} guests
            </span>
          )}
          {r.settings.hasBeverageMinimum && (
            <span className="rounded-full bg-blue-100 px-2 py-1 text-blue-800">
              🍹 1 drink min per person
            </span>
          )}
          {r.settings.excludedItems.length > 0 && (
            <span className="rounded-full bg-gray-100 px-2 py-1 text-gray-800">
              {r.settings.excludedItems[0]} excluded
            </span>
          )}
        </div>
        
        <div className="flex items-center justify-between pt-2">
          <div className="text-xs text-gray-500">{t.bookingFee} HK${getBookingFee(w.maxPax)}</div>
          <button onClick={onOpen} className="rounded-2xl bg-amber-900 px-3 py-1.5 text-sm text-white">{t.viewDetails}</button>
        </div>
      </div>
    </div>
  );
}

// -------- Enhanced Detail & Booking Modals --------
function DetailModal({open, onClose, r, windows, lang, onStart, user}){
  const ref = useFocusTrap(open);
  const t = i18n[lang];
  if(!open||!r) return null;
  const rows = windows.filter(w=>w.restaurantId===r.id && w.isActive && w.capRemaining>0);
  
  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center bg-black/40 p-4 md:items-center" role="dialog" aria-modal ref={ref}>
      <div className="max-h-[90vh] w-full max-w-3xl overflow-auto rounded-3xl bg-white p-4 md:p-6">
        <div className="flex items-center justify-between gap-4">
          <h3 className="text-xl font-semibold">{r.name}</h3>
          <button className="rounded-2xl border px-3 py-1" onClick={onClose}>Close</button>
        </div>
        <div className="mt-3 grid gap-4 md:grid-cols-2">
          <div className="rounded-2xl bg-gray-100 p-3">
            <img src={r.photos[0]} alt={r.name} className="h-56 w-full rounded-xl object-cover"/>
            <div className="mt-2 text-sm text-gray-600">{r.area} • {r.cuisine}</div>
            <p className="mt-2 text-sm">{r.blurb[lang]}</p>
            <div className="mt-2 text-xs">
              <a className="underline" href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(r.address)}`} target="_blank" rel="noreferrer">{t.copyMap}</a>
            </div>
          </div>
          <div className="space-y-4">
            <section>
              <h4 className="mb-1 font-medium">{t.included}</h4>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>30–50% off food items</li>
              </ul>
            </section>
            <section>
              <h4 className="mb-1 font-medium">{t.excluded}</h4>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>Alcohol</li>
                <li>Service charge</li>
                {r.settings.excludedItems.map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </section>
            <section>
              <h4 className="mb-1 font-medium">{t.rules}</h4>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                {r.rules.map((x,i)=>(<li key={i}>{x}</li>))}
                {r.settings.hasBeverageMinimum && <li>Each diner must order one beverage</li>}
                <li>{t.holdPolicy}</li>
              </ul>
            </section>
          </div>
        </div>
        <div className="mt-4 grid gap-3 md:grid-cols-2">
          <div className="flex flex-col gap-2 text-sm">
            {rows.map(w=> (
              <button key={w.id} className="flex items-center justify-between rounded-2xl border px-3 py-2 hover:bg-amber-50" onClick={()=>onStart(r, w.id)}>
                <span>🕑 {w.start}–{w.end}</span>
                <span className="rounded-xl border px-2 py-0.5 text-xs">{w.discount}%</span>
              </button>
            ))}
            {rows.length===0 && <div className="text-xs text-gray-500">No available windows.</div>}
          </div>
          <div className="self-center text-right text-sm text-gray-600">
            {t.bookingFee} <span className="text-gray-400">{t.feeNote}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function EnhancedBookingModal({open, onClose, r, w, lang, onConfirm, user}){
  const ref = useFocusTrap(open);
  const t = i18n[lang];
  const [pax, setPax] = React.useState(w? w.minPax : 2);
  const [name, setName] = React.useState(user?.name || '');
  const [email, setEmail] = React.useState(user?.email || '');
  const [phone, setPhone] = React.useState(user?.phone || '');
  const [agree, setAgree] = React.useState(false);
  const [processing, setProcessing] = React.useState(false);
  
  React.useEffect(()=>{ 
    if(w){ 
      setPax(w.minPax); 
      setAgree(false);
    }
  },[w]);
  
  if(!open||!r||!w) return null;
  
  const fee = getBookingFee(pax);
  const total = fee; // In a real app, this would include the meal cost
  
  async function submit(e){ 
    e.preventDefault(); 
    if(!agree) return; 
    
    setProcessing(true);
    
    try {
      // Create booking
      const booking = {
        id: uid(),
        windowId: w.id,
        restaurantId: r.id,
        userId: user?.id || '',
        name,
        email,
        phone,
        pax,
        lang,
        status: BOOKING_STATUS.ACTIVE,
        code: Math.random().toString(36).slice(2,8).toUpperCase(),
        paymentStatus: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Process payment (simulated)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, this would integrate with Stripe
      const paymentSuccess = Math.random() > 0.1; // 90% success rate
      
      if (paymentSuccess) {
        booking.paymentStatus = 'paid';
        onConfirm(booking);
      } else {
        booking.paymentStatus = 'failed';
        alert(t.paymentFailed);
      }
    } catch (error) {
      console.error('Booking error:', error);
      alert(t.paymentFailed);
    } finally {
      setProcessing(false);
    }
  }
  
  return (
    <div className="fixed inset-0 z-50 grid place-items-center bg-black/40 p-4" role="dialog" aria-modal ref={ref}>
      <form onSubmit={submit} className="w-full max-w-lg space-y-4 rounded-3xl bg-white p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">{r.name}</h3>
          <button type="button" onClick={onClose} className="rounded-2xl border px-3 py-1">Close</button>
        </div>
        
        <div className="grid gap-3 md:grid-cols-2">
          <label className="block text-sm">{t.selectWindow}
            <input readOnly value={`${w.start}–${w.end}`} className="mt-1 w-full rounded-2xl border px-3 py-2"/>
          </label>
          <label className="block text-sm">{t.selectPax}
            <select 
              value={pax} 
              onChange={e=>setPax(Number(e.target.value))}
              className="mt-1 w-full rounded-2xl border px-3 py-2"
              disabled={!user}
            >
              {Array.from({length: w.maxPax - w.minPax + 1}).map((_,i)=>{
                const v = w.minPax + i;
                const isMax = v === r.settings.maxPartySize;
                return (
                  <option key={v} value={v}>
                    {v} {isMax ? `(max)` : ''}
                  </option>
                );
              })}
            </select>
          </label>
        </div>
        
        {!user && (
          <div className="text-sm text-amber-700 bg-amber-50 p-3 rounded-xl">
            Please sign in to complete your booking
          </div>
        )}
        
        <div className="grid gap-3 md:grid-cols-2">
          <label className="block text-sm">{t.name}
            <input 
              required 
              value={name} 
              onChange={e=>setName(e.target.value)} 
              className="mt-1 w-full rounded-2xl border px-3 py-2"
              disabled={!!user}
            />
          </label>
          <label className="block text-sm">{t.email}
            <input 
              required 
              type="email" 
              value={email} 
              onChange={e=>setEmail(e.target.value)} 
              className="mt-1 w-full rounded-2xl border px-3 py-2"
              disabled={!!user}
            />
          </label>
          <label className="block text-sm md:col-span-2">{t.phone}
            <input 
              required 
              value={phone} 
              onChange={e=>setPhone(e.target.value)} 
              className="mt-1 w-full rounded-2xl border px-3 py-2"
              disabled={!!user}
            />
          </label>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-xl">
          <h4 className="font-medium mb-2">{t.bookingSummary}</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Discount</span>
              <span>{w.discount}% off</span>
            </div>
            <div className="flex justify-between">
              <span>Party size</span>
              <span>{pax} people</span>
            </div>
            <div className="flex justify-between">
              <span>{t.bookingFee}</span>
              <span>HK${fee}</span>
            </div>
            <div className="flex justify-between font-medium pt-2 border-t">
              <span>{t.total}</span>
              <span>HK${total}</span>
            </div>
          </div>
        </div>
        
        <label className="flex items-start gap-2 text-sm">
          <input type="checkbox" checked={agree} onChange={e=>setAgree(e.target.checked)} className="mt-1"/>
          <span>{t.agree}</span>
        </label>
        
        <button 
          type="submit" 
          disabled={!agree || processing || !user}
          className="w-full rounded-2xl bg-amber-900 px-4 py-2 text-white disabled:opacity-50"
        >
          {processing ? 'Processing...' : t.payNow}
        </button>
      </form>
    </div>
  );
}

// -------- My Bookings --------
function MyBookings({lang, bookings, restaurants, windows, onCancel, user}){
  const t = i18n[lang];
  const activeBookings = bookings.filter(b=>b.status===BOOKING_STATUS.ACTIVE);
  const pastBookings = bookings.filter(b=>b.status===BOOKING_STATUS.COMPLETED);
  
  function downloadICS(b){ 
    const r = restaurants.find(x=>x.id===b.restaurantId); 
    const w = windows.find(x=>x.id===b.windowId); 
    const blob = toICS(b, r, w); 
    const url = URL.createObjectURL(blob); 
    const a = document.createElement('a'); 
    a.href=url; 
    a.download=`BeforeSix_${r.name}_${b.code}.ics`; 
    document.body.appendChild(a); 
    a.click(); 
    a.remove(); 
    URL.revokeObjectURL(url); 
  }
  
  return (
    <section className="mx-auto max-w-6xl px-4 py-6">
      <h2 className="mb-3 text-lg font-semibold">{t.myBookings}</h2>
      
      {activeBookings.length > 0 && (
        <div className="mb-8">
          <h3 className="text-md font-medium mb-3">{t.upcoming}</h3>
          <div className="space-y-3">
            {activeBookings.map(b=>{ 
              const r = restaurants.find(x=>x.id===b.restaurantId); 
              const w = windows.find(x=>x.id===b.windowId); 
              return (
                <div key={b.id} className="flex flex-wrap items-center gap-3 rounded-2xl border p-4">
                  <div className="min-w-[200px]">
                    <div className="font-medium">{r.name}</div>
                    <div className="text-sm text-gray-600">🕑 {w.start}–{w.end}</div>
                    <div className="text-xs text-gray-500">{t.holdPolicy}</div>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm">{b.name} • {b.pax} people</div>
                    <div className="text-xs text-gray-500">Booking code: {b.code}</div>
                  </div>
                  <div className="ml-auto flex gap-2">
                    <button className="rounded-2xl border px-3 py-1 text-sm" onClick={()=>downloadICS(b)}>{t.addCalendar}</button>
                    <button className="rounded-2xl bg-red-600 px-3 py-1 text-sm text-white" onClick={()=>onCancel(b.id)}>{t.cancel}</button>
                  </div>
                </div>
              );})}
          </div>
        </div>
      )}
      
      {pastBookings.length > 0 && (
        <div>
          <h3 className="text-md font-medium mb-3">{t.past}</h3>
          <div className="space-y-3">
            {pastBookings.map(b=>{ 
              const r = restaurants.find(x=>x.id===b.restaurantId); 
              const w = windows.find(x=>x.id===b.windowId); 
              return (
                <div key={b.id} className="flex flex-wrap items-center gap-3 rounded-2xl border p-4 bg-gray-50">
                  <div className="min-w-[200px]">
                    <div className="font-medium">{r.name}</div>
                    <div className="text-sm text-gray-600">🕑 {w.start}–{w.end}</div>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm">{b.name} • {b.pax} people</div>
                    <div className="text-xs text-gray-500">Booking code: {b.code}</div>
                  </div>
                  <div className="ml-auto">
                    <span className="rounded-xl bg-green-100 px-3 py-1 text-xs text-green-800">{t.completed}</span>
                  </div>
                </div>
              );})}
          </div>
        </div>
      )}
      
      {bookings.length === 0 && (
        <div className="rounded-2xl border p-6 text-sm text-gray-600 text-center">
          {t.none}
        </div>
      )}
    </section>
  );
}

// -------- Enhanced Restaurant Dashboard --------
function EnhancedDashboard({lang, restaurants, windows, setWindows, setRestaurants, bookings, user}){
  const t = i18n[lang];
  const [selectedVenue, setSelectedVenue] = React.useState(null);
  const [activeTab, setActiveTab] = React.useState('overview');
  
  const userRestaurants = restaurants.filter(r => r.id === user?.restaurantId);
  
  const currentRestaurant = userRestaurants[0] || selectedVenue;
  
  const todaysBookings = bookings.filter(b=> 
    b.status === BOOKING_STATUS.ACTIVE && 
    windows.find(w=>w.id===b.windowId)?.restaurantId === currentRestaurant?.id
  );
  
  return (
    <section className="mx-auto max-w-6xl px-4 py-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">{t.dashboard}</h2>
        <p className="text-gray-600">Manage your restaurant's availability, bookings, and settings</p>
      </div>
      
      {!currentRestaurant ? (
        <div className="max-w-md space-y-3 rounded-2xl border p-4">
          <h3 className="text-lg font-semibold">{t.venueSelect}</h3>
          <div className="space-y-2">
            {restaurants.map(r => (
              <button
                key={r.id}
                className="w-full text-left rounded-xl border p-3 hover:bg-amber-50"
                onClick={() => setSelectedVenue(r)}
              >
                <div className="font-medium">{r.name}</div>
                <div className="text-sm text-gray-600">{r.area} • {r.cuisine}</div>
              </button>
            ))}
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Restaurant Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-semibold">{currentRestaurant.name}</h3>
              <p className="text-gray-600">{currentRestaurant.area} • {currentRestaurant.cuisine}</p>
            </div>
            <button
              className="rounded-xl border px-3 py-1 text-sm"
              onClick={() => setSelectedVenue(null)}
            >
              Change Restaurant
            </button>
          </div>
          
          {/* Tabs */}
          <div className="flex gap-2 border-b">
            {['overview', 'settings', 'availability', 'bookings'].map(tab => (
              <button
                key={tab}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === tab 
                    ? 'border-b-2 border-amber-900 text-amber-900' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {t[tab] || tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
          
          {/* Tab Content */}
          {activeTab === 'overview' && (
            <div className="grid gap-6 md:grid-cols-3">
              <div className="rounded-2xl border p-4">
                <h4 className="font-medium mb-2">Today's Bookings</h4>
                <div className="text-3xl font-bold">{todaysBookings.length}</div>
                <div className="text-sm text-gray-600">Active reservations</div>
              </div>
              
              <div className="rounded-2xl border p-4">
                <h4 className="font-medium mb-2">Available Slots</h4>
                <div className="text-3xl font-bold">
                  {windows.filter(w => 
                    w.restaurantId === currentRestaurant.id && 
                    w.isActive && 
                    w.capRemaining > 0
                  ).length}
                </div>
                <div className="text-sm text-gray-600">Time slots available</div>
              </div>
              
              <div className="rounded-2xl border p-4">
                <h4 className="font-medium mb-2">Revenue Potential</h4>
                <div className="text-3xl font-bold">HK$12,500</div>
                <div className="text-sm text-gray-600">Estimated monthly</div>
              </div>
            </div>
          )}
          
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="rounded-2xl border p-6">
                <h4 className="text-lg font-medium mb-4">{t.settings}</h4>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">{t.maxPartySize}</label>
                    <select 
                      value={currentRestaurant.settings.maxPartySize}
                      onChange={e => {
                        const updated = restaurants.map(r => 
                          r.id === currentRestaurant.id 
                            ? { 
                                ...r, 
                                settings: { 
                                  ...r.settings, 
                                  maxPartySize: Number(e.target.value) 
                                } 
                              } 
                            : r
                        );
                        setRestaurants(updated);
                      }}
                      className="w-full rounded-2xl border px-3 py-2"
                    >
                      {[4, 5, 6].map(size => (
                        <option key={size} value={size}>{size} people</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="flex items-center gap-3">
                      <input 
                        type="checkbox" 
                        checked={currentRestaurant.settings.hasBeverageMinimum}
                        onChange={e => {
                          const updated = restaurants.map(r => 
                            r.id === currentRestaurant.id 
                              ? { 
                                  ...r, 
                                  settings: { 
                                    ...r.settings, 
                                    hasBeverageMinimum: e.target.checked 
                                  } 
                                } 
                              : r
                          );
                          setRestaurants(updated);
                        }}
                        className="h-5 w-5 rounded border-amber-900"
                      />
                      <span>{t.beverageMin}</span>
                    </label>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">{t.menuExclusions}</label>
                    <div className="flex flex-wrap gap-2">
                      {['Premium steaks', 'Wine pairing', 'Omakase', 'Premium sashimi'].map(item => (
                        <button
                          key={item}
                          className={`rounded-full px-3 py-1 text-sm ${
                            currentRestaurant.settings.excludedItems.includes(item)
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                          onClick={() => {
                            const currentExclusions = currentRestaurant.settings.excludedItems;
                            const updated = restaurants.map(r => 
                              r.id === currentRestaurant.id 
                                ? { 
                                    ...r, 
                                    settings: { 
                                      ...r.settings, 
                                      excludedItems: currentExclusions.includes(item)
                                        ? currentExclusions.filter(i => i !== item)
                                        : [...currentExclusions, item]
                                    } 
                                  } 
                                : r
                            );
                            setRestaurants(updated);
                          }}
                        >
                          {item}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'availability' && (
            <div className="space-y-6">
              <div className="rounded-2xl border p-6">
                <h4 className="text-lg font-medium mb-4">{t.timeSlots}</h4>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">{t.date}</label>
                      <input type="date" className="w-full rounded-2xl border px-3 py-2" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">{t.start}</label>
                      <input type="time" className="w-full rounded-2xl border px-3 py-2" defaultValue="17:00" />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">{t.end}</label>
                      <input type="time" className="w-full rounded-2xl border px-3 py-2" defaultValue="18:00" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">{t.discountPct}</label>
                      <select className="w-full rounded-2xl border px-3 py-2">
                        <option value="30">30%</option>
                        <option value="40">40%</option>
                        <option value="50">50%</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Min pax</label>
                      <input type="number" min="2" max="6" className="w-full rounded-2xl border px-3 py-2" defaultValue="2" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Max pax</label>
                      <input 
                        type="number" 
                        min="2" 
                        max={currentRestaurant.settings.maxPartySize}
                        defaultValue={currentRestaurant.settings.maxPartySize}
                        className="w-full rounded-2xl border px-3 py-2" 
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Capacity</label>
                    <input type="number" min="1" className="w-full rounded-2xl border px-3 py-2" defaultValue="4" />
                  </div>
                  
                  <button className="rounded-2xl bg-amber-900 px-4 py-2 text-white">
                    {t.publish}
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'bookings' && (
            <div className="space-y-6">
              <div className="rounded-2xl border p-6">
                <h4 className="text-lg font-medium mb-4">{t.todaysBookings}</h4>
                
                {todaysBookings.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {t.none}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {todaysBookings.map(b => (
                      <div key={b.id} className="flex items-center justify-between p-4 border rounded-xl">
                        <div>
                          <div className="font-medium">{b.name}</div>
                          <div className="text-sm text-gray-600">{b.pax} people • {b.phone}</div>
                          <div className="text-xs text-gray-500">Code: {b.code}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="rounded-full bg-green-100 px-3 py-1 text-xs text-green-800">
                            {t.active}
                          </span>
                          <button className="text-red-600 hover:text-red-800">
                            ❌
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </section>
  );
}

// -------- Customer Account --------
function CustomerAccount({lang, user, bookings, restaurants, onLogout}){
  const t = i18n[lang];
  const [activeTab, setActiveTab] = React.useState('profile');
  
  const userBookings = bookings.filter(b => b.userId === user?.id);
  const activeBookings = userBookings.filter(b => b.status === BOOKING_STATUS.ACTIVE);
  const pastBookings = userBookings.filter(b => b.status === BOOKING_STATUS.COMPLETED);
  
  return (
    <section className="mx-auto max-w-6xl px-4 py-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">{t.account}</h2>
        <p className="text-gray-600">Manage your profile, bookings, and preferences</p>
      </div>
      
      <div className="grid gap-6 lg:grid-cols-4">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-24 rounded-2xl border p-4">
            <div className="text-center mb-4">
              <div className="mx-auto h-16 w-16 rounded-full bg-amber-100 flex items-center justify-center text-2xl mb-2">
                {user?.name?.charAt(0).toUpperCase() || 'U'}
              </div>
              <h3 className="font-semibold">{user?.name || 'User'}</h3>
              <p className="text-sm text-gray-600">{user?.email}</p>
            </div>
            
            <nav className="space-y-1">
              {['profile', 'bookings', 'favorites', 'notifications'].map(tab => (
                <button
                  key={tab}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium ${
                    activeTab === tab 
                      ? 'bg-amber-900 text-white' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                  onClick={() => setActiveTab(tab)}
                >
                  {t[tab] || tab.charAt(0).toUpperCase() + tab.slice(1)}
                </button>
              ))}
            </nav>
            
            <button
              onClick={onLogout}
              className="w-full mt-4 rounded-xl border px-3 py-2 text-sm text-gray-600 hover:bg-gray-50"
            >
              {t.logout}
            </button>
          </div>
        </div>
        
        {/* Main Content */}
        <div className="lg:col-span-3">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <div className="rounded-2xl border p-6">
                <h3 className="text-lg font-semibold mb-4">{t.profile}</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Name</label>
                    <input 
                      type="text" 
                      value={user?.name || ''}
                      className="w-full rounded-2xl border px-3 py-2"
                      placeholder="Enter your name"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Email</label>
                    <input 
                      type="email" 
                      value={user?.email || ''}
                      className="w-full rounded-2xl border px-3 py-2"
                      placeholder="Enter your email"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Phone</label>
                    <input 
                      type="tel" 
                      value={user?.phone || ''}
                      className="w-full rounded-2xl border px-3 py-2"
                      placeholder="+852"
                    />
                  </div>
                  
                  <button className="rounded-2xl bg-amber-900 px-4 py-2 text-white">
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'bookings' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">{t.bookingHistory}</h3>
                
                {activeBookings.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-medium mb-3">{t.upcoming}</h4>
                    <div className="space-y-3">
                      {activeBookings.map(b => {
                        const r = restaurants.find(x => x.id === b.restaurantId);
                        return (
                          <div key={b.id} className="flex items-center justify-between p-4 border rounded-xl">
                            <div>
                              <div className="font-medium">{r?.name}</div>
                              <div className="text-sm text-gray-600">{b.pax} people • Code: {b.code}</div>
                            </div>
                            <div className="flex gap-2">
                              <button className="text-blue-600 hover:text-blue-800">
                                View Details
                              </button>
                              <button className="text-red-600 hover:text-red-800">
                                Cancel
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
                
                {pastBookings.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3">{t.past}</h4>
                    <div className="space-y-3">
                      {pastBookings.map(b => {
                        const r = restaurants.find(x => x.id === b.restaurantId);
                        return (
                          <div key={b.id} className="flex items-center justify-between p-4 border rounded-xl bg-gray-50">
                            <div>
                              <div className="font-medium">{r?.name}</div>
                              <div className="text-sm text-gray-600">{b.pax} people • Code: {b.code}</div>
                            </div>
                            <span className="rounded-full bg-green-100 px-3 py-1 text-xs text-green-800">
                              {t.completed}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
                
                {userBookings.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    {t.none}
                  </div>
                )}
              </div>
            </div>
          )}
          
          {activeTab === 'favorites' && (
            <div className="space-y-6">
              <div className="rounded-2xl border p-6">
                <h3 className="text-lg font-semibold mb-4">{t.favorites}</h3>
                
                <div className="text-center py-8 text-gray-500">
                  No favorite restaurants yet
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div className="rounded-2xl border p-6">
                <h3 className="text-lg font-semibold mb-4">{t.notifications}</h3>
                
                <div className="space-y-4">
                  <label className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Email notifications</div>
                      <div className="text-sm text-gray-600">Receive booking confirmations and updates</div>
                    </div>
                    <input type="checkbox" defaultChecked className="h-5 w-5 rounded border-amber-900" />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">SMS notifications</div>
                      <div className="text-sm text-gray-600">Receive booking reminders</div>
                    </div>
                    <input type="checkbox" className="h-5 w-5 rounded border-amber-900" />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Promotional offers</div>
                      <div className="text-sm text-gray-600">Receive special deals and discounts</div>
                    </div>
                    <input type="checkbox" className="h-5 w-5 rounded border-amber-900" />
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}

// -------- Footer --------
function Footer({lang}){
  const t = i18n[lang];
  return (
    <footer className="border-t bg-gray-50 py-12">
      <div className="mx-auto max-w-6xl px-4">
        <div className="grid gap-8 md:grid-cols-4">
          <div>
            <h3 className="font-serif text-xl font-bold mb-4">{t.brand}</h3>
            <p className="text-sm text-gray-600 mb-4">
              Early seats, better nights — 30–50% off food at Hong Kong's best restaurants.
            </p>
            <div className="text-xs text-gray-500">
              {t.companyReg}
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Company</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="text-gray-600 hover:text-gray-900">{t.about}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">{t.faq}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">{t.contact}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">{t.terms}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">{t.privacy}</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Support</h4>
            <ul className="space-y-2 text-sm">
              <li className="text-gray-600">{t.supportPhone}</li>
              <li className="text-gray-600">{t.supportEmail}</li>
              <li className="text-gray-600">{t.supportHours}</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Connect</h4>
            <div className="flex gap-4">
              <a href="#" className="text-gray-600 hover:text-gray-900">FB</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">IG</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">TW</a>
            </div>
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t text-center text-xs text-gray-500">
          {t.copyright}
        </div>
      </div>
    </footer>
  );
}

// ---------------- Root ----------------
function App(){
  const [lang, setLang] = usePersistentState("lang", "en");
  const [restaurants, setRestaurants] = usePersistentState("restaurants", SEED_RESTAURANTS);
  const [windows, setWindows] = usePersistentState("windows", SEED_WINDOWS);
  const [bookings, setBookings] = usePersistentState("bookings", []);
  const [view, setView] = usePersistentState("view", "explore");
  
  // Auth state
  const { user, loading } = useAuth();
  const [authModalOpen, setAuthModalOpen] = React.useState(false);
  const [authMode, setAuthMode] = React.useState('signin'); // 'signin' or 'signup'
  
  // Modal states
  const [detailR, setDetailR] = React.useState(null);
  const [bookW, setBookW] = React.useState(null);
  
  // Set global language for i18n
  React.useEffect(() => {
    window.currentLang = lang;
  }, [lang]);
  
  function handleLogin() {
    setAuthModalOpen(true);
    setAuthMode('signin');
  }
  
  function handleRegister() {
    setAuthModalOpen(true);
    setAuthMode('signup');
  }
  
  function handleLogout() {
    signOut();
  }
  
  function handleAuthComplete() {
    setAuthModalOpen(false);
  }
  
  function onConfirm(b){
    setBookings([b, ...bookings]);
    setWindows(windows.map(w=> w.id===b.windowId? {...w, capRemaining: Math.max(0, w.capRemaining-1)} : w));
    setDetailR(null); 
    setBookW(null);
    setView('bookings');
    
    // Create ICS auto-download
    const r = restaurants.find(x=>x.id===b.restaurantId); 
    const w = windows.find(x=>x.id===b.windowId); 
    const url = URL.createObjectURL(toICS(b,r,w)); 
    const a = document.createElement('a'); 
    a.href=url; 
    a.download=`BeforeSix_${r.name}_${b.code}.ics`; 
    document.body.appendChild(a); 
    a.click(); 
    a.remove(); 
    URL.revokeObjectURL(url);
  }
  
  function onCancel(id){ 
    setBookings(bookings.map(b=> b.id===id? {...b, status:'cancelled'}: b)); 
  }
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen" style={{background:"linear-gradient(180deg, #FFFBF2 0%, #FFFFFF 40%)"}}>
      <Header 
        lang={lang} 
        setLang={setLang} 
        view={view} 
        setView={setView}
        user={user}
        onLogin={handleLogin}
        onRegister={handleRegister}
        onLogout={handleLogout}
      />
      
      <main>
        <div className="mx-auto max-w-6xl px-4 py-6">
          <Hero lang={lang} />
        </div>
        
        {view==='explore' && (
          <Explore 
            lang={lang} 
            restaurants={restaurants} 
            windows={windows} 
            onOpen={(r)=>{setDetailR(r);}} 
            user={user}
          />
        )}
        
        {view==='bookings' && (
          <MyBookings 
            lang={lang} 
            bookings={bookings} 
            restaurants={restaurants} 
            windows={windows} 
            onCancel={onCancel}
            user={user}
          />
        )}
        
        {view==='dashboard' && user?.role === USER_ROLES.RESTAURANT && (
          <EnhancedDashboard 
            lang={lang} 
            restaurants={restaurants} 
            windows={windows} 
            setWindows={setWindows} 
            setRestaurants={setRestaurants}
            bookings={bookings}
            user={user}
          />
        )}
        
        {view==='account' && user?.role === USER_ROLES.CUSTOMER && (
          <CustomerAccount 
            lang={lang}
            user={user}
            bookings={bookings}
            restaurants={restaurants}
            onLogout={handleLogout}
          />
        )}
      </main>

      <DetailModal 
        open={!!detailR} 
        onClose={()=>setDetailR(null)} 
        r={detailR} 
        windows={windows} 
        lang={lang} 
        onStart={(r,wid)=>{ 
          setDetailR(null); 
          setBookW(windows.find(w=>w.id===wid) || null); 
        }} 
      />
      
      <EnhancedBookingModal 
        open={!!bookW} 
        onClose={()=>setBookW(null)} 
        r={bookW? restaurants.find(r=>r.id===bookW.restaurantId) || null : null} 
        w={bookW} 
        lang={lang} 
        onConfirm={onConfirm}
        user={user}
      />
      
      <AuthModal
        open={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        mode={authMode}
        onModeChange={setAuthMode}
        onAuthComplete={handleAuthComplete}
      />
      
      <Footer lang={lang} />
    </div>
  );
}

const container = document.getElementById('root');
const root = ReactDOM.createRoot(container);
root.render(<App />);
