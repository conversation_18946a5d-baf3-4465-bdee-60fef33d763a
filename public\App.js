// BEFORE SIX — Single‑file demo app (no backend). 
// • Explorer (search + filters)  • Booking flow (HK$80 fee, 10‑min hold, T&Cs) 
// • My bookings  • Restaurant Dashboard (publish windows + rules, see bookings)
// • Bilingual EN/繁 toggle  • LocalStorage persistence  • Accessible modals
// TailwindCSS expected in host environment.

// ---------------- i18n ----------------
const i18n = {
  en: {
    brand: "Before Six",
    subtitle: "Early seats, better nights — 30–50% off food",
    explore: "Explore",
    myBookings: "My bookings",
    dashboard: "Restaurant dashboard",
    tonight: "Tonight’s Early Seats",
    filters: "Filters",
    area: "Area",
    cuisine: "Cuisine",
    discount: "Discount",
    time: "Time",
    party: "Party size",
    sortBy: "Sort",
    sort_relevance: "Relevance",
    sort_soonest: "Soonest",
    sort_discount: "Highest discount",
    clearAll: "Clear all",
    results: (n)=>`${n} results`,
    bookingFee: "Booking fee",
    feeNote: "(charged by us; restaurant settles your bill)",
    holdPolicy: "Your table is held for 10 minutes after your window begins.",
    included: "What’s included",
    excluded: "What’s not included",
    rules: "House rules",
    viewDetails: "Open ticket",
    reserve: "Reserve seat",
    selectWindow: "Choose an arrival window",
    selectPax: "Party size",
    name: "Name",
    email: "Email",
    phone: "Phone",
    agree: "I agree to the venue’s T&Cs, including the 10‑minute hold.",
    confirm: (fee) => `Confirm (HK$${fee})`,
    successTitle: "You’re in!",
    successBody: "We’ve emailed your ticket. Add it to your calendar.",
    langToggle: "繁",
    noResults: "No results — try adjusting filters.",
    manage: "Manage",
    cancel: "Cancel booking",
    cancelled: "Cancelled",
    active: "Active",
    addCalendar: "Add to calendar (.ics)",
    copyMap: "Open map",
    globalPolicy: "Global policy: 10‑minute hold on all bookings.",
    // Dashboard
    dash_login: "Sign in as restaurant",
    yourEmail: "Your work email",
    sendMagic: "Send magic link (demo)",
    signedInAs: (n)=>`Signed in as ${n}`,
    venueSelect: "Select venue",
    newWindow: "Publish window",
    start: "Start",
    end: "End",
    discountPct: "Discount %",
    minPax: "Min pax",
    maxPax: "Max pax",
    cap: "Cap",
    pattern: "Pattern",
    once: "One‑off (date)",
    weekly: "Weekly (weekday)",
    date: "Date",
    weekday: "Weekday",
    publish: "Publish",
    ruleTemplates: "Quick rules",
    rule_bev: "Each diner must order a beverage",
    rule_time: "Time limit 90 minutes",
    rule_foodOnly: "Discount applies to food only",
    rulesSaved: "Rules saved",
    todaysBookings: "Today’s bookings",
    status: "Status",
    none: "None",
    save: "Save",
    delete: "Delete",
  },
  zh: {
    brand: "Before Six",
    subtitle: "早到好味道 — 食物 30–50% 折扣",
    explore: "探索",
    myBookings: "我的訂座",
    dashboard: "餐廳後台",
    tonight: "今晚早到座位",
    filters: "篩選",
    area: "區域",
    cuisine: "菜系",
    discount: "折扣",
    time: "時段",
    party: "人數",
    sortBy: "排序",
    sort_relevance: "相關度",
    sort_soonest: "最快時間",
    sort_discount: "最高折扣",
    clearAll: "清除全部",
    results: (n)=>`${n} 個結果`,
    bookingFee: "訂座手續費",
    feeNote: "（由平台收取；餐廳現場結帳）",
    holdPolicy: "到席時段開始後保留 10 分鐘。",
    included: "優惠包括",
    excluded: "不包括",
    rules: "用餐條款",
    viewDetails: "開啟票劵",
    reserve: "預留座位",
    selectWindow: "選擇到席時段",
    selectPax: "選擇人數",
    name: "姓名",
    email: "電郵",
    phone: "電話",
    agree: "本人同意餐廳條款（包括 10 分鐘保留期）。",
    confirm: (fee) => `確認（HK$${fee}）`,
    successTitle: "已預留！",
    successBody: "確認信已發送。加入行事曆以免錯過。",
    langToggle: "EN",
    noResults: "未有結果，試試調整篩選。",
    manage: "管理",
    cancel: "取消訂座",
    cancelled: "已取消",
    active: "生效中",
    addCalendar: "加入行事曆 (.ics)",
    copyMap: "開啟地圖",
    globalPolicy: "平台統一政策：所有訂座均設 10 分鐘保留期。",
    // Dashboard
    dash_login: "以餐廳身份登入",
    yourEmail: "工作電郵",
    sendMagic: "發送登入連結（示範）",
    signedInAs: (n)=>`登入為 ${n}`,
    venueSelect: "選擇餐廳",
    newWindow: "發佈時段",
    start: "開始",
    end: "結束",
    discountPct: "折扣 %",
    minPax: "最少人數",
    maxPax: "最多人數",
    cap: "名額",
    pattern: "模式",
    once: "單次（日期）",
    weekly: "每週（星期）",
    date: "日期",
    weekday: "星期",
    publish: "發佈",
    ruleTemplates: "快捷條款",
    rule_bev: "每位需點一款飲品",
    rule_time: "用餐時間 90 分鐘",
    rule_foodOnly: "折扣只適用於食物",
    rulesSaved: "已儲存條款",
    todaysBookings: "今日訂座",
    status: "狀態",
    none: "沒有",
    save: "儲存",
    delete: "刪除",
  }
}

// ---------------- Types ----------------
/** Window pattern */
// type Pattern = "once" | "weekly";
// interface WindowSlot {
//   id: string; restaurantId: string; pattern: Pattern;
//   date?: string; // YYYY-MM-DD when pattern=once
//   dow?: number; // 0-6 (Sun..Sat) when pattern=weekly
//   start: string; end: string; // HH:mm
//   discount: 30|40|50; minPax: number; maxPax: number; cap: number; capRemaining: number;
//   isActive: boolean;
// }
// interface Restaurant {
//   id: string; name: string; area: string; address: string; lat?: number; lng?: number;
//   cuisine: string; priceBand?: string; blurb: {en:string, zh:string};
//   photos: string[];
//   rules: string[]; // free text lines; always contains global food-only unless overridden
// }
// interface Booking { id: string; windowId: string; restaurantId: string; name: string; email: string; phone: string; pax: number; lang: keyof typeof i18n; status: "active"|"cancelled"; code: string; createdAt: string; }

// ---------------- Utilities ----------------
const uid = () => Math.random().toString(36).slice(2);
const todayISO = () => new Date().toISOString().slice(0,10);
function dowOf(dateISO){ return new Date(dateISO+"T00:00:00").getDay(); }
function toICS(booking, rest, slot){
  // simplistic ICS generator
  const startDate = new Date(`${slot.date ?? todayISO()}T${slot.start}:00`);
  const endDate = new Date(`${slot.date ?? todayISO()}T${slot.end}:00`);
  const dt = (d)=>d.toISOString().replace(/[-:]/g,"").split(".")[0]+"Z";
  const body = [
    "BEGIN:VCALENDAR","VERSION:2.0","PRODID:-//Before Six//Booking//EN","BEGIN:VEVENT",
    `UID:${booking.id}@beforesix.hk`,
    `DTSTAMP:${dt(new Date())}`,
    `DTSTART:${dt(startDate)}`,
    `DTEND:${dt(endDate)}`,
    `SUMMARY:Before Six — ${rest.name}`,
    `DESCRIPTION:${rest.address} — Hold 10 mins after window start`,
    "END:VEVENT","END:VCALENDAR"
  ].join("\r\n");
  return new Blob([body], {type:"text/calendar"});
}

function getBookingFee(pax) {
  switch (pax) {
    case 2:
      return 50;
    case 3:
      return 70;
    case 4:
      return 80;
    case 5:
      return 100;
    case 6:
      return 120;
    default:
      return 0;
  }
}

// ---------------- Demo seed data ----------------
const SEED_RESTAURANTS = [
  { id:"r1", name:"Kin & Coal", area:"Central", address:"18 Staunton St, Central", cuisine:"Modern European", blurb:{en:"Seasonal grill & small plates in SoHo.", zh:"主打時令火烤與精緻小食，位於 SoHo。"}, photos:["https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1200&auto=format&fit=crop"], rules:["Each diner must order one beverage","Discount applies to food only"] },
  { id:"r2", name:"Umi Izakaya", area:"Tsim Sha Tsui", address:"Harbour City, TST", cuisine:"Japanese", blurb:{en:"Charcoal seafood izakaya.", zh:"炭火海鮮居酒屋。"}, photos:["https://images.unsplash.com/photo-1559339351-3794a0af83d1?q=80&w=1200&auto=format&fit=crop"], rules:["Each diner must order one beverage","Selected items excluded","Discount applies to food only"] },
  { id:"r3", name:"Pasta Via", area:"Wan Chai", address:"Jaffe Rd, Wan Chai", cuisine:"Italian", blurb:{en:"Fresh pasta, neighborhood vibe.", zh:"手造意粉，街坊小店氛圍。"}, photos:["https://images.unsplash.com/photo-1517959105821-eaf2591984f5?q=80&w=1200&auto=format&fit=crop"], rules:["Discount applies to food only"] },
];
const SEED_WINDOWS = [
  { id:"w1", restaurantId:"r1", pattern:"once", date: todayISO(), start:"17:00", end:"18:00", discount:40, minPax:2, maxPax:4, cap:4, capRemaining:4, isActive:true },
  { id:"w2", restaurantId:"r1", pattern:"once", date: todayISO(), start:"21:30", end:"22:30", discount:40, minPax:2, maxPax:4, cap:2, capRemaining:2, isActive:true },
  { id:"w3", restaurantId:"r2", pattern:"once", date: todayISO(), start:"17:30", end:"18:30", discount:50, minPax:2, maxPax:6, cap:6, capRemaining:6, isActive:true },
  { id:"w4", restaurantId:"r3", pattern:"once", date: todayISO(), start:"18:00", end:"18:45", discount:30, minPax:2, maxPax:4, cap:3, capRemaining:3, isActive:true },
];

// ---------------- Storage (localStorage) ----------------
function usePersistentState(key, initial){
  const [state, setState] = React.useState(()=>{
    const raw = localStorage.getItem(key);
    return raw? JSON.parse(raw): initial;
  });
  React.useEffect(()=>{ localStorage.setItem(key, JSON.stringify(state)); },[key,state]);
  return [state, setState];
}

// ---------------- UI helpers ----------------
function Header({lang, setLang, view, setView}){
  const t = i18n[lang];
  return (
    <header className="sticky top-0 z-40 border-b bg-white/80 backdrop-blur">
      <div className="mx-auto flex max-w-6xl items-center justify-between gap-4 px-4 py-3">
        <div className="flex items-baseline gap-3">
          <div className="font-serif text-2xl font-bold tracking-tight">{t.brand}</div>
          <div className="text-xs text-amber-800/80">HK</div>
        </div>
        <nav className="flex items-center gap-4 text-sm">
          <button className={`rounded-xl px-3 py-1 ${view==='explore'? 'bg-amber-900 text-white':'border'}`} onClick={()=>setView('explore')} aria-current={view==='explore'}>{t.explore}</button>
          <button className={`rounded-xl px-3 py-1 ${view==='bookings'? 'bg-amber-900 text-white':'border'}`} onClick={()=>setView('bookings')}>{t.myBookings}</button>
          <button className={`rounded-xl px-3 py-1 ${view==='dashboard'? 'bg-amber-900 text-white':'border'}`} onClick={()=>setView('dashboard')}>{t.dashboard}</button>
          <button onClick={()=>setLang(lang==='en'?'zh':'en')} className="rounded-xl border px-3 py-1" aria-label="Toggle language">{i18n[lang].langToggle}</button>
        </nav>
      </div>
    </header>
  );
}

function Hero({lang}){
  const t = i18n[lang];
  return (
    <section className="relative mx-auto max-w-6xl overflow-hidden rounded-3xl border bg-gradient-to-br from-amber-50 via-white to-amber-100 p-6 md:p-10">
      <div className="pointer-events-none absolute -left-10 -top-10 h-56 w-56 rounded-full bg-amber-200/40 blur-3xl" />
      <div className="pointer-events-none absolute -bottom-12 -right-8 h-72 w-72 rounded-full bg-yellow-200/30 blur-3xl" />
      <div className="relative flex flex-col gap-3">
        <div className="text-xs uppercase tracking-wider text-amber-700/70">Hong Kong • Dining</div>
        <h1 className="font-serif text-3xl font-bold tracking-tight md:text-5xl">{t.brand}</h1>
        <p className="max-w-xl text-sm text-gray-700 md:text-base">{t.subtitle}</p>
        <div className="text-xs text-amber-800/80">{t.holdPolicy}</div>
      </div>
    </section>
  );
}

// -------- Explore (filters + list) --------
function Explore({lang, restaurants, windows, onOpen}){
  const t = i18n[lang];
  const [filters, setFilters] = usePersistentState("filters", {area:"", cuisine:"", discount:0, party:2, sort:"relevance"});

  const list = React.useMemo(()=>{
    const today = todayISO();
    const active = windows.filter(w=> w.isActive && (w.date? w.date>=today : true) && w.capRemaining>0);
    const rows = active.map(w=>({w, r: restaurants.find(r=>r.id===w.restaurantId)})).filter(x=>!!x.r);
    let filtered = rows.filter(({r,w})=>{
      const okArea = !filters.area || r.area.toLowerCase().includes(filters.area.toLowerCase());
      const okCui = !filters.cuisine || r.cuisine.toLowerCase().includes(filters.cuisine.toLowerCase());
      const okDis = !filters.discount || w.discount>=filters.discount;
      const okPax = filters.party<=w.maxPax;
      return okArea && okCui && okDis && okPax;
    });
    if(filters.sort==='soonest') filtered.sort((a,b)=> (a.w.start).localeCompare(b.w.start));
    if(filters.sort==='discount') filtered.sort((a,b)=> b.w.discount - a.w.discount);
    return filtered;
  },[restaurants, windows, filters]);

  return (
    <section className="mx-auto max-w-6xl px-4 py-6">
      <h2 className="mb-3 text-lg font-semibold">{t.tonight}</h2>
      <div className="mb-3 grid grid-cols-2 gap-3 md:grid-cols-8">
        <input className="rounded-2xl border px-3 py-2" placeholder={t.area} value={filters.area} onChange={e=>setFilters({...filters, area:e.target.value})} />
        <input className="rounded-2xl border px-3 py-2" placeholder={t.cuisine} value={filters.cuisine} onChange={e=>setFilters({...filters, cuisine:e.target.value})} />
        <select className="rounded-2xl border px-3 py-2" value={filters.discount} onChange={e=>setFilters({...filters, discount:Number(e.target.value)})}>
          <option value={0}>{t.discount}</option>
          <option value={30}>≥ 30%</option>
          <option value={40}>≥ 40%</option>
          <option value={50}>≥ 50%</option>
        </select>
        <select className="rounded-2xl border px-3 py-2" value={filters.party} onChange={e=>setFilters({...filters, party:Number(e.target.value)})}>
          {[2,3,4,5,6].map(v=> <option key={v} value={v}>{v}</option>)}
        </select>
        <select className="rounded-2xl border px-3 py-2" value={filters.sort} onChange={e=>setFilters({...filters, sort:e.target.value})}>
          <option value="relevance">{t.sort_relevance}</option>
          <option value="soonest">{t.sort_soonest}</option>
          <option value="discount">{t.sort_discount}</option>
        </select>
        <button className="rounded-2xl border px-3 py-2" onClick={()=>setFilters({area:"", cuisine:"", discount:0, party:2, sort:"relevance"})}>{t.clearAll}</button>
        <div className="self-center text-sm text-gray-600">{t.results(list.length)}</div>
      </div>

      {list.length===0? (
        <div className="rounded-3xl border p-8 text-center text-sm text-gray-600">{t.noResults}</div>
      ): (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {list.map(({r,w})=> (
            <Card key={w.id} r={r} w={w} lang={lang} onOpen={()=>onOpen(r)} />
          ))}
        </div>
      )}

      <footer className="mt-8 text-center text-xs text-gray-600">{t.bookingFee} · {t.holdPolicy}</footer>
    </section>
  );
}

function Card({r,w,lang,onOpen}){
  const t = i18n[lang];
  return (
    <div className="overflow-hidden rounded-3xl bg-white shadow-[0_8px_24px_rgba(0,0,0,0.06)]">
      <img src={r.photos[0]} alt={r.name} className="h-44 w-full object-cover"/>
      <div className="space-y-3 p-4">
        <div className="flex items-start justify-between gap-3">
          <div>
            <h3 className="text-lg font-semibold">{r.name}</h3>
            <div className="text-sm text-gray-600">{r.area} • {r.cuisine}</div>
            <p className="mt-1 text-sm text-gray-700">{r.blurb[lang]}</p>
          </div>
          <div className="shrink-0 rounded-2xl border px-3 py-1 text-xs">{w.discount}% off</div>
        </div>
        <div className="flex items-center gap-2 text-xs">
          <span className="grid h-5 w-5 place-items-center rounded-full border">🕑</span>
          <span>{w.start}–{w.end}</span>
          <span className="ml-auto text-gray-500">2–{w.maxPax} pax</span>
        </div>
        <div className="flex items-center justify-between pt-2">
          <div className="text-xs text-gray-500">{t.bookingFee}</div>
          <button onClick={onOpen} className="rounded-2xl bg-amber-900 px-3 py-1.5 text-sm text-white">{t.viewDetails}</button>
        </div>
      </div>
    </div>
  );
}

// -------- Detail & Booking Modals --------
function useFocusTrap(open){
  const ref = React.useRef(null);
  React.useEffect(()=>{
    if(!open) return; const el = ref.current; if(!el) return;
    const prev = document.activeElement;
    const focusable = el.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    focusable[0]?.focus();
    function handler(e){ if(e.key==='Tab'){ const f = Array.from(focusable); const i = f.indexOf(document.activeElement); if(e.shiftKey){ if(i<=0){ e.preventDefault(); f[f.length-1]?.focus(); }} else { if(i===f.length-1){ e.preventDefault(); f[0]?.focus(); } } } }
    el.addEventListener('keydown', handler);
    return ()=>{ el.removeEventListener('keydown', handler); prev?.focus(); };
  },[open]);
  return ref;
}

function DetailModal({open, onClose, r, windows, lang, onStart}){
  const ref = useFocusTrap(open);
  const t = i18n[lang];
  if(!open||!r) return null;
  const rows = windows.filter(w=>w.restaurantId===r.id && w.isActive && w.capRemaining>0);
  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center bg-black/40 p-4 md:items-center" role="dialog" aria-modal ref={ref}>
      <div className="max-h-[90vh] w-full max-w-3xl overflow-auto rounded-3xl bg-white p-4 md:p-6">
        <div className="flex items-center justify-between gap-4">
          <h3 className="text-xl font-semibold">{r.name}</h3>
          <button className="rounded-2xl border px-3 py-1" onClick={onClose}>Close</button>
        </div>
        <div className="mt-3 grid gap-4 md:grid-cols-2">
          <div className="rounded-2xl bg-gray-100 p-3">
            <img src={r.photos[0]} alt={r.name} className="h-56 w-full rounded-xl object-cover"/>
            <div className="mt-2 text-sm text-gray-600">{r.area} • {r.cuisine}</div>
            <p className="mt-2 text-sm">{r.blurb[lang]}</p>
            <div className="mt-2 text-xs"><a className="underline" href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(r.address)}`} target="_blank" rel="noreferrer">Map</a></div>
          </div>
          <div className="space-y-4">
            <section>
              <h4 className="mb-1 font-medium">{t.included}</h4>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>30–50% off food items</li>
              </ul>
            </section>
            <section>
              <h4 className="mb-1 font-medium">{t.excluded}</h4>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>Alcohol</li><li>Service charge</li>
              </ul>
            </section>
            <section>
              <h4 className="mb-1 font-medium">{t.rules}</h4>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                {r.rules.map((x,i)=>(<li key={i}>{x}</li>))}
                <li>{t.holdPolicy}</li>
              </ul>
            </section>
          </div>
        </div>
        <div className="mt-4 grid gap-3 md:grid-cols-2">
          <div className="flex flex-col gap-2 text-sm">
            {rows.map(w=> (
              <button key={w.id} className="flex items-center justify-between rounded-2xl border px-3 py-2 hover:bg-amber-50" onClick={()=>onStart(r, w.id)}>
                <span>🕑 {w.start}–{w.end}</span>
                <span className="rounded-xl border px-2 py-0.5 text-xs">{w.discount}%</span>
              </button>
            ))}
            {rows.length===0 && <div className="text-xs text-gray-500">No available windows.</div>}
          </div>
          <div className="self-center text-right text-sm text-gray-600">
            {t.bookingFee} <span className="text-gray-400">{t.feeNote}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function BookingModal({open, onClose, r, w, lang, onConfirm}){
  const ref = useFocusTrap(open);
  const t = i18n[lang];
  const [pax, setPax] = React.useState(w? w.minPax : 2);
  const [name, setName] = React.useState("");
  const [email, setEmail] = React.useState("");
  const [phone, setPhone] = React.useState("");
  const [agree, setAgree] = React.useState(false);
  React.useEffect(()=>{ if(w){ setPax(w.minPax); setAgree(false);} },[w]);
  if(!open||!r||!w) return null;
  const fee = getBookingFee(pax);
  function submit(e){ e.preventDefault(); if(!agree) return; const b={ id:uid(), windowId:w.id, restaurantId:r.id, name, email, phone, pax, lang, status:"active", code: Math.random().toString(36).slice(2,8).toUpperCase(), createdAt:new Date().toISOString()}; onConfirm(b); }
  return (
    <div className="fixed inset-0 z-50 grid place-items-center bg-black/40 p-4" role="dialog" aria-modal ref={ref}>
      <form onSubmit={submit} className="w-full max-w-lg space-y-4 rounded-3xl bg-white p-6">
        <div className="flex items-center justify-between"><h3 className="text-lg font-semibold">{r.name}</h3><button type="button" onClick={onClose} className="rounded-2xl border px-3 py-1">Close</button></div>
        <div className="grid gap-3 md:grid-cols-2">
          <label className="block text-sm">{t.selectWindow}<input readOnly value={`${w.start}–${w.end}`} className="mt-1 w-full rounded-2xl border px-3 py-2"/></label>
          <label className="block text-sm">{t.selectPax}
            <select value={pax} onChange={e=>setPax(Number(e.target.value))} className="mt-1 w-full rounded-2xl border px-3 py-2">
              {Array.from({length:w.maxPax - w.minPax + 1}).map((_,i)=>{const v=w.minPax+i; return <option key={v} value={v}>{v}</option>;})}
            </select>
          </label>
        </div>
        <div className="grid gap-3 md:grid-cols-2">
          <label className="block text-sm">{t.name}<input required value={name} onChange={e=>setName(e.target.value)} className="mt-1 w-full rounded-2xl border px-3 py-2"/></label>
          <label className="block text-sm">{t.email}<input required type="email" value={email} onChange={e=>setEmail(e.target.value)} className="mt-1 w-full rounded-2xl border px-3 py-2"/></label>
          <label className="block text-sm md:col-span-2">{t.phone}<input required value={phone} onChange={e=>setPhone(e.target.value)} className="mt-1 w-full rounded-2xl border px-3 py-2"/></label>
        </div>
        <label className="flex items-start gap-2 text-sm"><input type="checkbox" checked={agree} onChange={e=>setAgree(e.target.checked)} className="mt-1"/><span>{t.agree}</span></label>
        <div className="flex items-center justify-between"><div className="text-sm text-gray-600">{t.bookingFee} HK${fee}</div><button disabled={!agree} className="rounded-2xl bg-amber-900 px-4 py-2 text-white disabled:opacity-40">{t.confirm(fee)}</button></div>
      </form>
    </div>
  );
}

// -------- My Bookings --------
function MyBookings({lang, bookings, restaurants, windows, onCancel}){
  const t = i18n[lang];
  const rows = bookings.filter(b=>b.status==='active');
  function downloadICS(b){ const r = restaurants.find(x=>x.id===b.restaurantId); const w = windows.find(x=>x.id===b.windowId); const blob = toICS(b, r, w); const url = URL.createObjectURL(blob); const a = document.createElement('a'); a.href=url; a.download=`BeforeSix_${r.name}_${b.code}.ics`; document.body.appendChild(a); a.click(); a.remove(); URL.revokeObjectURL(url); }
  return (
    <section className="mx-auto max-w-6xl px-4 py-6">
      <h2 className="mb-3 text-lg font-semibold">{t.myBookings}</h2>
      {rows.length===0? <div className="rounded-2xl border p-6 text-sm text-gray-600">{i18n[lang].none}</div> : (
        <div className="space-y-3">
          {rows.map(b=>{ const r = restaurants.find(x=>x.id===b.restaurantId); const w = windows.find(x=>x.id===b.windowId); return (
            <div key={b.id} className="flex flex-wrap items-center gap-3 rounded-2xl border p-4">
              <div className="min-w-[200px]">
                <div className="font-medium">{r.name}</div>
                <div className="text-sm text-gray-600">🕑 {w.start}–{w.end}</div>
                <div className="text-xs text-gray-500">{t.holdPolicy}</div>
              </div>
              <div className="ml-auto flex gap-2">
                <button className="rounded-2xl border px-3 py-1 text-sm" onClick={()=>downloadICS(b)}>{t.addCalendar}</button>
                <button className="rounded-2xl bg-red-600 px-3 py-1 text-sm text-white" onClick={()=>onCancel(b.id)}>{t.cancel}</button>
              </div>
            </div>
          );})}
        </div>
      )}
    </section>
  );
}

// -------- Dashboard (demo) --------
function Dashboard({lang, restaurants, windows, setWindows, setRestaurants, bookings}){
  const t = i18n[lang];
  const [email, setEmail] = React.useState("");
  const [me, setMe] = usePersistentState("dash_me", undefined);
  const [venueId, setVenueId] = usePersistentState("dash_venue", undefined);
  const venue = restaurants.find(r=>r.id===venueId);

  function publishWindow(data){
    if(!venue) return;
    const slot = {
      id: uid(), restaurantId: venue.id, pattern: data.pattern||"once", date: data.date, dow: data.dow,
      start: data.start||"17:00", end: data.end||"18:00", discount: (data.discount||30),
      minPax: data.minPax||2, maxPax: data.maxPax||4, cap: data.cap||2, capRemaining: data.cap||2, isActive: true
    };
    setWindows([slot, ...windows]);
  }
  function saveRules(next){ if(!venue) return; setRestaurants(restaurants.map(r=> r.id===venue.id? {...r, rules: next} : r)); alert(t.rulesSaved); }

  const todays = bookings.filter(b=> b.status==='active' && windows.find(w=>w.id===b.windowId)?.restaurantId===venueId);

  return (
    <section className="mx-auto max-w-6xl px-4 py-6">
      {!me ? (
        <div className="max-w-md space-y-3 rounded-2xl border p-4">
          <h3 className="text-lg font-semibold">{t.dash_login}</h3>
          <label className="block text-sm">{t.yourEmail}<input value={email} onChange={e=>setEmail(e.target.value)} placeholder="<EMAIL>" className="mt-1 w-full rounded-2xl border px-3 py-2"/></label>
          <button onClick={()=> setMe(email||"<EMAIL>")} className="rounded-2xl bg-amber-900 px-4 py-2 text-white">{t.sendMagic}</button>
        </div>
      ):(
        <div className="space-y-4">
          <div className="flex flex-wrap items-center gap-3">
            <div className="rounded-2xl border px-3 py-1 text-sm">{t.signedInAs(me)}</div>
            <select className="rounded-2xl border px-3 py-1" value={venueId} onChange={e=>setVenueId(e.target.value)}>
              <option value="">{t.venueSelect}</option>
              {restaurants.map(r=> <option key={r.id} value={r.id}>{r.name}</option>)}
            </select>
          </div>

          {venue && (
            <div className="grid gap-6 md:grid-cols-2">
              {/* Publish window */}
              <div className="space-y-2 rounded-2xl border p-4">
                <h4 className="font-medium">{t.newWindow}</h4>
                <Row>
                  <Select label={t.pattern} value={"once"} options={[{v:"once",t:i18n[lang].once},{v:"weekly",t:i18n[lang].weekly}]} onChange={()=>{}} disabled/>
                  <Input label={t.date} type="date" defaultValue={todayISO()} id="date"/>
                </Row>
                <Row>
                  <Input label={t.start} defaultValue="17:00" id="start"/>
                  <Input label={t.end} defaultValue="18:00" id="end"/>
                </Row>
                <Row>
                  <Select label={t.discountPct} value={"40"} options={[{v:"30",t:"30"},{v:"40",t:"40"},{v:"50",t:"50"}]} onChange={()=>{}} disabled/>
                  <Input label={t.cap} type="number" defaultValue="4" id="cap"/>
                </Row>
                <Row>
                  <Input label={t.minPax} type="number" defaultValue="2" id="min"/>
                  <label className="block text-sm">{t.maxPax}<select id="max" defaultValue="4" className="mt-1 w-full rounded-2xl border px-3 py-2">{[2,3,4,5,6].map(v=> <option key={v} value={v}>{v}</option>)}</select></label>
                </Row>
                <button className="rounded-2xl bg-amber-900 px-4 py-2 text-white" onClick={()=>{
                  const g = (id)=> (document.getElementById(id));
                  publishWindow({ pattern:"once", date:g('date').value, start:g('start').value, end:g('end').value, discount:40, cap:Number(g('cap').value), minPax:Number(g('min').value), maxPax:Number(g('max').value) });
                }}>{t.publish}</button>
              </div>

              {/* House rules */}
              <div className="space-y-2 rounded-2xl border p-4">
                <h4 className="font-medium">{t.rules}</h4>
                <div className="flex flex-wrap gap-2 text-sm">
                  <RuleChip label={i18n[lang].rule_bev} onAdd={()=>saveRules(Array.from(new Set([...venue.rules, i18n[lang].rule_bev])))} />
                  <RuleChip label={i18n[lang].rule_time} onAdd={()=>saveRules(Array.from(new Set([...venue.rules, i18n[lang].rule_time])))} />
                  <RuleChip label={i18n[lang].rule_foodOnly} onAdd={()=>saveRules(Array.from(new Set([...venue.rules, i18n[lang].rule_foodOnly])))} />
                </div>
                <ul className="list-disc pl-5 text-sm">
                  {venue.rules.map((rul, i)=>(<li key={i}>{rul}</li>))}
                </ul>
              </div>

              {/* Today bookings */}
              <div className="md:col-span-2 space-y-2 rounded-2xl border p-4">
                <h4 className="font-medium">{t.todaysBookings}</h4>
                {todays.length===0? <div className="text-sm text-gray-600">{i18n[lang].none}</div> : (
                  <div className="divide-y">
                    {todays.map(b=> (
                      <div key={b.id} className="flex flex-wrap items-center justify-between gap-2 py-2 text-sm">
                        <div>{b.name} • {b.pax}p • {b.phone}</div>
                        <span className="rounded-xl border px-2 py-0.5 text-xs">{i18n[lang].active}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </section>
  );
}

function RuleChip({label,onAdd}){ return <button className="rounded-xl border px-3 py-1 hover:bg-amber-50" onClick={onAdd}>{label}</button>; }
function Row({children}){ return <div className="grid grid-cols-2 gap-3">{children}</div>; }
function Input({label,id,type="text",defaultValue}){ return <label className="block text-sm">{label}<input id={id} type={type} defaultValue={defaultValue} className="mt-1 w-full rounded-2xl border px-3 py-2"/></label>; }
function Select({label,value,options,onChange,disabled}){ return <label className="block text-sm">{label}<select disabled={disabled} value={value} onChange={e=>onChange(e.target.value)} className="mt-1 w-full rounded-2xl border px-3 py-2">{options.map(o=> <option key={o.v} value={o.v}>{o.t}</option>)}</select></label>; }

// ---------------- Root ----------------
function App(){
  const [lang, setLang] = usePersistentState("lang", "en");
  const [restaurants, setRestaurants] = usePersistentState("restaurants", SEED_RESTAURANTS);
  const [windows, setWindows] = usePersistentState("windows", SEED_WINDOWS);
  const [bookings, setBookings] = usePersistentState("bookings", []);
  const [view, setView] = usePersistentState("view", "explore");

  const [detailR, setDetailR] = React.useState(null);
  const [bookW, setBookW] = React.useState(null);

  function onConfirm(b){
    setBookings([b, ...bookings]);
    setWindows(windows.map(w=> w.id===b.windowId? {...w, capRemaining: Math.max(0, w.capRemaining-1)} : w));
    setDetailR(null); setBookW(null);
    setView('bookings');
    // Create ICS auto-download
    const r = restaurants.find(x=>x.id===b.restaurantId); const w = windows.find(x=>x.id===b.windowId); const url = URL.createObjectURL(toICS(b,r,w)); const a = document.createElement('a'); a.href=url; a.download=`BeforeSix_${r.name}_${b.code}.ics`; document.body.appendChild(a); a.click(); a.remove(); URL.revokeObjectURL(url);
  }
  function onCancel(id){ setBookings(bookings.map(b=> b.id===id? {...b, status:'cancelled'}: b)); }

  return (
    <div className="min-h-screen" style={{background:"linear-gradient(180deg, #FFFBF2 0%, #FFFFFF 40%)"}}>
      <Header lang={lang} setLang={setLang} view={view} setView={setView} />
      <main>
        <div className="mx-auto max-w-6xl px-4 py-6">
          <Hero lang={lang} />
        </div>
        {view==='explore' && <Explore lang={lang} restaurants={restaurants} windows={windows} onOpen={(r)=>{setDetailR(r);}} />}
        {view==='bookings' && <MyBookings lang={lang} bookings={bookings} restaurants={restaurants} windows={windows} onCancel={onCancel} />}
        {view==='dashboard' && <Dashboard lang={lang} restaurants={restaurants} windows={windows} setWindows={setWindows} setRestaurants={setRestaurants} bookings={bookings} />}
      </main>

      <DetailModal open={!!detailR} onClose={()=>setDetailR(null)} r={detailR} windows={windows} lang={lang} onStart={(r,wid)=>{ setDetailR(null); setBookW(windows.find(w=>w.id===wid) || null); }} />
      <BookingModal open={!!bookW} onClose={()=>setBookW(null)} r={bookW? restaurants.find(r=>r.id===bookW.restaurantId) || null : null} w={bookW} lang={lang} onConfirm={onConfirm} />

      <footer className="border-t py-6 text-center text-xs text-gray-600">{i18n[lang].bookingFee} · {i18n[lang].globalPolicy}</footer>
    </div>
  );
}

const container = document.getElementById('root');
const root = ReactDOM.createRoot(container);
root.render(<App />);
