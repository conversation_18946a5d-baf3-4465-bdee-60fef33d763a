#!/usr/bin/env python3
"""
Simple HTTP server to serve the Before Six application
Run with: python serve.py
Then open: http://localhost:8000
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# Configuration
PORT = 8000
DIRECTORY = "public"

class Handler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        # Add CORS headers to allow local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    # Check if public directory exists
    if not os.path.exists(DIRECTORY):
        print(f"Error: {DIRECTORY} directory not found!")
        print("Make sure you're running this from the project root directory.")
        return
    
    # Check if required files exist
    required_files = ['index.html', 'App.js']
    for file in required_files:
        if not os.path.exists(os.path.join(DIRECTORY, file)):
            print(f"Error: {file} not found in {DIRECTORY} directory!")
            return
    
    print(f"Starting server on port {PORT}...")
    print(f"Serving files from: {os.path.abspath(DIRECTORY)}")
    print(f"Open your browser to: http://localhost:{PORT}")
    print("Press Ctrl+C to stop the server")
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            # Try to open browser automatically
            try:
                webbrowser.open(f'http://localhost:{PORT}')
            except:
                pass  # Browser opening failed, but that's okay
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
    except OSError as e:
        if e.errno == 10048:  # Port already in use on Windows
            print(f"Error: Port {PORT} is already in use!")
            print("Try using a different port or stop the other server.")
        else:
            print(f"Error starting server: {e}")

if __name__ == "__main__":
    main()
