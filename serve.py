#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
import os
import sys

# Change to the public directory
os.chdir('public')

# Configure the server
PORT = 8000
Handler = http.server.SimpleHTTPRequestHandler

print(f"Starting server at http://localhost:{PORT}")
print("Serving Before Six application...")

# Create the server
with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print("Server started successfully!")
    print("Press Ctrl+C to stop the server")
    
    # Open the browser automatically
    webbrowser.open(f'http://localhost:{PORT}')
    
    # Start serving
    httpd.serve_forever()
