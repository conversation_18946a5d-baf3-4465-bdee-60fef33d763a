@echo off
echo Starting Before Six application...
echo.

:: Try Node.js first (most reliable)
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Node.js server...
    call npm start
    goto :end
)

:: Try Python
where python >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Python server...
    cd public
    python -m http.server 8000
    goto :end
)

:: Try Python3
where python3 >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Python3 server...
    cd public
    python3 -m http.server 8000
    goto :end
)

echo Error: No suitable server found!
echo Please install Node.js or Python to run the application.
echo.
echo Recommended: Install Node.js from https://nodejs.org/
echo Then run: npm start
pause
goto :eof

:end
echo.
echo Server stopped. Press any key to close...
pause >nul
