# Before Six - Hong Kong Dining Deals Platform

A complete restaurant booking platform offering early dining discounts (30-50% off) at Hong Kong's best restaurants.

## Features

### For Customers
- **Restaurant Discovery**: Browse restaurants with detailed filtering
- **Enhanced Booking Flow**: Complete booking process with payment simulation
- **Account Management**: Profile management, booking history, and favorites
- **Multilingual Support**: Full Traditional Chinese language support
- **Mobile-Optimized**: Perfect experience on all devices

### For Restaurants
- **Dashboard**: Complete partner dashboard for managing availability
- **Settings**: Configure maximum party size (4-6 people), beverage minimums, and menu exclusions
- **Time Slot Management**: Publish and manage dining time slots
- **Booking Management**: View and manage customer reservations
- **Revenue Tracking**: Monitor booking statistics and revenue potential

## Technology Stack

- **Frontend**: React with Babel Standalone
- **Authentication**: Supabase (ready for integration)
- **Payment**: Stripe integration ready
- **Styling**: TailwindCSS
- **Server**: Node.js http-server (recommended) or Python

## Quick Start

### Option 1: Using the Batch File (Easiest)
Double-click `start-server.bat` in the project root directory. This will automatically detect and use the best available server.

### Option 2: Using Node.js (Recommended)
1. Install Node.js from https://nodejs.org/
2. Open terminal in the project directory
3. Run `npm start`
4. Your browser will automatically open to http://localhost:8000

### Option 3: Using Python
1. Open terminal in the project directory
2. Run `python serve.py`
3. Or run `cd public && python -m http.server 8000`
4. Open http://localhost:8000 in your browser

## Project Structure

```
Before Six/
├── public/                 # Frontend application files
│   ├── index.html         # Main HTML file
│   └── App.js            # React application code
├── serve.py              # Python server script
├── package.json          # Node.js configuration
├── start-server.bat      # Windows batch file for easy startup
└── README.md            # This file
```

## Configuration

### Supabase Setup
To enable real authentication, update these values in `App.js`:
```javascript
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = 'your-anon-key';
```

### Stripe Setup
For real payment processing, configure your Stripe publishable key in the booking modal.

## Development

### Adding New Restaurants
Edit the `SEED_RESTAURANTS` array in `App.js` to add new restaurant data.

### Customizing Styling
The application uses TailwindCSS classes. You can modify the styling directly in the component files.

### Language Support
All text is managed in the `i18n` object at the top of `App.js`. Add new translations to both `en` and `zh` objects.

## Troubleshooting

### Seeing Directory Listing Instead of App
This happens when the server doesn't properly serve index.html. Try:
1. Use the Node.js server (`npm start`) - most reliable
2. Ensure you're in the correct directory
3. Check that index.html exists in the public folder

### JavaScript Errors
Make sure all required CDN scripts are loaded in index.html:
- React
- ReactDOM
- Babel Standalone
- Supabase
- Stripe

### Authentication Not Working
1. Verify Supabase URL and anon key are correct
2. Check that your Supabase project is properly configured
3. Ensure CORS settings allow your domain

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

© 2025 Before Six. All rights reserved.

## Support

For technical support, please contact:
- Email: <EMAIL>
- Phone: +852 1234 5678
- Hours: Mon-Fri 9am-6pm, Sat-Sun 10am-4pm
