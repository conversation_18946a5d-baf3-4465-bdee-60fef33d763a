# Before Six

A restaurant booking platform for early dining slots with discounts.

## Features

- **Explorer**: Search and filter restaurants with early dining slots
- **Booking Flow**: Reserve tables with HK$80 booking fee and 10-minute hold policy
- **My Bookings**: Manage your reservations
- **Restaurant Dashboard**: For restaurants to publish time windows and manage bookings
- **Bilingual Support**: English/Traditional Chinese toggle
- **Local Storage**: Persistent data storage in browser

## How to Run

### Option 1: Using Python (Recommended)

1. Make sure you have Python installed on your system
2. Open a terminal/command prompt in the project directory
3. Run the server:
   ```bash
   python serve.py
   ```
4. Open your browser to: http://localhost:8000

### Option 2: Using the Batch File (Windows)

1. Double-click `start-server.bat`
2. Your browser should automatically open to http://localhost:8000

### Option 3: Using Node.js (Alternative)

If you have Node.js installed, you can use:
```bash
npx http-server public -p 8000 -o
```

## Why You Need a Local Server

This application cannot be run by simply opening `index.html` in a browser because:

1. **CORS Restrictions**: Modern browsers block loading local JavaScript modules for security
2. **CDN Dependencies**: The app loads React, ReactDOM, and Babel from CDNs
3. **Module Loading**: Babel needs to transpile the JSX code at runtime

## Technical Details

- **Frontend**: React 18 with Babel transpilation
- **Styling**: TailwindCSS (loaded from CDN)
- **State Management**: React hooks with localStorage persistence
- **No Backend**: All data is stored locally in the browser

## Project Structure

```
Before Six/
├── public/
│   ├── index.html    # Main HTML file with CDN imports
│   └── App.js        # Complete React application
├── serve.py          # Python development server
├── start-server.bat  # Windows batch file to start server
└── README.md         # This file
```

## Troubleshooting

### No UI/UX Showing?

1. **Check TailwindCSS**: Make sure the TailwindCSS CDN is loaded in index.html
2. **Use a Web Server**: Don't open index.html directly - use the Python server
3. **Check Console**: Open browser developer tools to see any JavaScript errors
4. **Port Conflicts**: If port 8000 is busy, edit `serve.py` to use a different port

### Common Issues

- **Port 8000 in use**: Change the PORT variable in `serve.py`
- **Python not found**: Install Python from python.org
- **Browser cache**: Try hard refresh (Ctrl+F5) or open in incognito mode

## Demo Data

The application comes with seed data including:
- 3 sample restaurants (Kin & Coal, Umi Izakaya, Pasta Via)
- Multiple time windows for today
- Booking fee structure (HK$50-120 based on party size)

All data is stored in localStorage and persists between sessions.
